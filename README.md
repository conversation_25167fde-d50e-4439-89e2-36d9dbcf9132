# Budget Tracker Next

A comprehensive, full-featured family-collaborative budget tracking and expense management application built with modern technologies. This application bridges the gap between personal finance management and family financial collaboration, providing secure, role-based multi-user financial management.

## 🎯 Beta Program - Finance Type Separation

**NEW**: Complete beta program implementation with three distinct finance journey types:

- **Personal Finance**: Clean, individual-focused experience (6 dashboard pages)
- **Family Finance**: Collaboration-first design for household management (6 dashboard pages)  
- **Combined Finance**: Power user experience with full access (6+ dashboard pages)

The beta program features 18+ dashboard pages with context-driven architecture, capability-based rendering, and seamless switching between real and mock data modes. All beta features are isolated under `/beta` routes with zero impact on the production system.

## ✨ Core Features

### 🏠 Personal Finance Management
- **Dashboard**: Comprehensive financial overview with key metrics, interactive charts, and recent activity
- **Transaction Management**: Advanced income/expense tracking with categorization and detailed analytics
- **Budget Management**: Category-based budgets with monitoring, alerts, and period tracking (monthly/weekly/yearly)
- **Account Management**: Multiple financial accounts support (checking, savings, credit cards, cash) with balance tracking
- **Category Management**: Customizable hierarchical income/expense categories with professional icons
- **Financial Goals**: Sophisticated savings goals with progress tracking and target date management
- **Analytics & Reporting**: Advanced spending patterns, trends, and financial health insights with data visualization

### 👨‍👩‍👧‍👦 Family Collaboration Features
- **Family Groups**: Create and manage multiple family financial groups with secure data sharing
- **Role-Based Access**: Granular permissions system (owner, admin, member) for family group management
- **Invitation System**: Secure email-based invitations with cryptographic tokens and expiration handling
- **Shared Financial Data**: Collaborative management of family-wide accounts, budgets, transactions, categories, and goals
- **Context Switching**: Seamless toggle between personal and family group financial views
- **Real-time Collaboration**: Multi-user financial management with data synchronization
- **Member Management**: Comprehensive family member administration and role assignment

### 🔐 Security & User Experience
- **Advanced Authentication**: Secure user authentication with Supabase Auth and middleware protection
- **Row-Level Security**: Comprehensive database-level access control with RLS policies
- **Professional UI**: Modern, responsive design with dark/light theme support
- **Mobile-First Design**: Optimized experience across all device types
- **Professional Iconography**: Consistent use of Lucide React icons (no emojis)
- **Real-time Notifications**: Toast notifications and loading states throughout the application

## 🚀 Technology Stack

### **Core Framework**
- **[Next.js 15](https://nextjs.org/)** with App Router and React 19
- **[TypeScript](https://www.typescriptlang.org/)** with strict mode for comprehensive type safety
- **Node.js 20+** runtime requirement

### **Database & Backend**
- **[Supabase](https://supabase.com/)** (PostgreSQL) with comprehensive Row-Level Security policies
- **Supabase Auth** for secure user authentication and session management
- **Supabase Storage** for avatar and file management
- **40+ Database Migrations** with comprehensive audit trail and schema evolution

### **Frontend Technologies**
- **[Tailwind CSS](https://tailwindcss.com/)** for utility-first styling with custom animations
- **[shadcn/ui](https://ui.shadcn.com/)** component library (27+ professionally designed components)
- **[Lucide React](https://lucide.dev/)** for consistent professional iconography
- **[next-themes](https://github.com/pacocoursey/next-themes)** for seamless dark/light mode switching
- **[Recharts](https://recharts.org/)** for advanced data visualization and charts

### **State Management & Forms**
- **[TanStack Query (React Query)](https://tanstack.com/query/latest)** for server state management and caching
- **React Context** for application state (UserSettings, MobileMenu, AppMode)
- **[React Hook Form](https://react-hook-form.com/)** with [Zod](https://zod.dev/) validation for robust form handling
- **[date-fns](https://date-fns.org/)** for comprehensive date manipulation

### **Development & Quality Tools**
- **ESLint** with TypeScript rules and import sorting
- **PostCSS** with Tailwind CSS processing
- **Path aliases** (`@/` → `src/`) for clean imports
- **Comprehensive type definitions** with generated Supabase types

## 🚀 Getting Started

### Prerequisites

- **Node.js 20+** installed on your system
- **npm** package manager
- **Supabase account** and project setup
- **Git** for version control

### Quick Start Installation

1. **Clone the repository:**
```bash
git clone https://github.com/yourusername/budget-tracker-next.git
cd budget-tracker-next
```

2. **Install dependencies:**
```bash
npm install
```

3. **Environment Configuration:**
Create a `.env.local` file in the root directory:
```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Application Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

4. **Database Setup:**
   - Access your Supabase dashboard
   - Execute migrations from the `supabase/migrations` directory
   - Verify Row Level Security (RLS) policies are enabled
   - Confirm all RPC functions are properly deployed

5. **Launch Development Server:**
```bash
npm run dev
```

6. **Access the Application:**
Open [http://localhost:3000](http://localhost:3000) to start using the application

### Development Workflow

```bash
# Development commands
npm run dev          # Start development server (port 3000)
npm run build        # Create production build
npm run start        # Start production server
npm run lint         # Run ESLint code quality checks
npm run type-check   # Run TypeScript type validation
```

## 🏗️ Architecture & Project Structure

### **Feature-Based Architecture**
This application follows a sophisticated feature-based architecture pattern that promotes code organization, maintainability, and scalability.

```
budget-tracker-next/
├── src/
│   ├── app/                    # Next.js 15 App Router (Route Handlers)
│   │   ├── auth/              # Authentication pages (login, signup, password reset)
│   │   ├── beta/              # Beta program routes (complete implementation)
│   │   │   ├── auth/          # Beta authentication flow
│   │   │   ├── dashboard/     # Beta dashboard with 18+ pages
│   │   │   │   ├── personal/  # Personal finance suite (6 pages)
│   │   │   │   ├── family/    # Family finance suite (6 pages)
│   │   │   │   ├── combined/  # Combined finance suite (6 pages)
│   │   │   │   ├── unified/   # Unified analytics
│   │   │   │   └── ai-insights/ # AI-powered insights
│   │   │   └── onboarding/    # Beta user onboarding
│   │   ├── dashboard/         # Protected application pages
│   │   │   ├── accounts/      # Account management pages
│   │   │   ├── analytics/     # Financial analytics and reporting
│   │   │   ├── budgets/       # Budget management pages
│   │   │   ├── categories/    # Category management pages
│   │   │   ├── family/        # Family collaboration features
│   │   │   │   └── [groupId]/ # Dynamic family group routes
│   │   │   ├── goals/         # Financial goals pages
│   │   │   ├── invitations/   # Invitation management
│   │   │   └── transactions/  # Transaction management pages
│   │   ├── invite/[token]/    # Public invitation acceptance
│   │   └── layout.tsx         # Root layout with providers
│   │
│   ├── beta/                  # Beta program implementation (100% complete)
│   │   ├── components/        # Beta-specific UI components
│   │   ├── contexts/          # Beta state management (BetaFinanceTypeContext)
│   │   ├── config/            # Finance journey configurations
│   │   ├── dashboards/        # Finance-type specific components
│   │   │   ├── personal/      # Personal finance components & services
│   │   │   ├── family/        # Family finance components & services
│   │   │   └── combined/      # Combined finance components & services
│   │   ├── services/          # Beta-specific services (real + mock data)
│   │   ├── types/             # Beta TypeScript definitions
│   │   └── migrations/        # Beta database schema updates
│   │
│   ├── features/              # Feature-Based Organization
│   │   ├── dashboard/         # Dashboard feature (64KB)
│   │   │   ├── components/    # Dashboard-specific components
│   │   │   ├── services/      # Dashboard data services
│   │   │   └── types/         # Dashboard type definitions
│   │   ├── family-groups/     # Family collaboration (136KB)
│   │   │   ├── components/    # Family group components
│   │   │   ├── services/      # Family group services
│   │   │   └── types/         # Family group types
│   │   └── transactions/      # Transaction services (12KB)
│   │
│   ├── shared/               # Shared Application Resources
│   │   ├── components/       # Reusable components
│   │   │   ├── ui/          # shadcn/ui components (27+ components)
│   │   │   └── layout/      # Header, Sidebar, Navigation
│   │   ├── contexts/        # React contexts (UserSettings, MobileMenu, AppMode)
│   │   ├── hooks/           # Custom React hooks
│   │   ├── services/        # Core application services
│   │   │   └── supabase/   # Supabase client, server actions, RPC functions
│   │   ├── types/          # Global TypeScript definitions
│   │   └── utils/          # Utility functions and helpers
│   │
│   └── lib/                 # Third-party configurations
│       ├── supabase.ts     # Supabase client configuration
│       └── utils.ts        # Utility functions
│
├── public/                  # Static assets
├── supabase/               # Database configuration
│   └── migrations/         # 40+ database migrations
│
└── Configuration Files
    ├── tailwind.config.js  # Tailwind CSS configuration
    ├── tsconfig.json       # TypeScript configuration
    ├── next.config.js      # Next.js configuration
    └── .env.local          # Environment variables
```

### **Key Architectural Patterns**

#### **1. Feature-Based Organization**
- **Isolated Features**: Each feature maintains its own components, services, and types
- **Barrel Exports**: Clean imports via `index.ts` files
- **Service Layer Pattern**: Dedicated services for database operations and business logic
- **Component-Service-Type Structure**: Consistent organization across features

#### **2. Data Access Layer**
- **TanStack Query**: All server state management with caching and synchronization
- **Server Actions**: Secure mutations in `server-actions.ts`
- **RPC Functions**: Complex database queries with type safety
- **Generated Types**: Automatic TypeScript types from Supabase schema

#### **3. Family Group Context System**
- **Dual-Mode Operation**: Seamless switching between personal and family contexts
- **Route-Based Mode Detection**: Automatic context switching via URL patterns
- **Shared Data Model**: `family_group_id` columns for multi-tenancy
- **Role-Based Access Control**: Granular permissions (owner, admin, member)

## 🗄️ Database Architecture

### **Comprehensive PostgreSQL Schema**
The application utilizes a sophisticated PostgreSQL database architecture with **40+ migrations** and comprehensive **Row-Level Security (RLS)** policies.

#### **Core Financial Tables**
```sql
user_profiles              # Extended user information
├── UUID primary key (links to auth.users)
├── username, avatar_url, currency, language settings
└── Automatic creation via database trigger

accounts                   # Financial accounts management
├── UUID primary key, user_id, family_group_id (nullable)
├── name, type, balance, currency, icon, color
├── RLS policies for personal/family access control
└── Support for multiple account types (checking, savings, credit, cash)

categories                 # Income/expense categorization
├── UUID primary key, user_id, family_group_id (nullable)
├── name, type (income/expense), icon, color
├── parent_id for hierarchical category structure
└── Custom categories with professional icon system

transactions              # Financial transaction records
├── UUID primary key, user_id, account_id, category_id
├── amount, type, description, date, notes
├── family_group_id for shared transaction visibility
└── Complex RLS policies for multi-user access

budgets                   # Budget management system
├── UUID primary key, user_id, category_id, family_group_id (nullable)
├── amount, period (monthly/weekly/yearly)
├── start_date, end_date tracking
└── Budget monitoring and alert system

financial_goals           # Goal tracking system
├── UUID primary key, user_id, family_group_id (nullable)
├── name, target_amount, current_amount, target_date
├── status (active/completed/paused)
└── Progress tracking and milestone management
```

#### **Family Collaboration Schema**
```sql
family_groups             # Family group management
├── UUID primary key, name, owner_user_id
├── created_at, updated_at timestamps
└── Group-level settings and preferences

family_group_members      # Member management system
├── Composite primary key (group_id, user_id)
├── role (member, admin, owner) with granular permissions
├── joined_at timestamp for audit trail
└── Member status and activity tracking

family_group_invitations  # Secure invitation system
├── UUID primary key, group_id, invitee_email
├── status (pending, accepted, expired, revoked)
├── Cryptographically secure token with expiration
├── invited_by_user_id for audit trail
└── Email verification and security measures

beta_feedback             # Beta program feedback collection
├── UUID primary key, user_id, feedback_type, content
├── beta_user, finance_type, onboarding_completed fields
├── Comprehensive RLS policies for beta program
└── Analytics functions for beta insights
```

#### **Advanced Database Features**
- **Row-Level Security (RLS)**: Comprehensive policies on all tables
- **Database Functions (RPC)**: 20+ custom functions for complex operations
- **Audit Trail**: Complete change tracking with timestamps
- **Data Integrity**: Foreign key constraints and cascade rules
- **Performance Optimization**: Strategic indexing and query optimization

## 🔧 Development Scripts

```bash
# Core Development Commands
npm run dev          # Start development server on port 3000
npm run build        # Create optimized production build
npm run start        # Start production server
npm run lint         # Run ESLint with TypeScript rules
npm run type-check   # Run TypeScript type validation

# Recommended Development Workflow
npm run type-check && npm run lint && npm run build
```

## 🚀 Deployment

### **Netlify Deployment (Recommended)**
The application is optimized for [Netlify](https://netlify.com) deployment:

1. **Repository Setup**: Push your code to a GitHub repository
2. **Import to Netlify**: Import the project to your Netlify dashboard
3. **Environment Variables**: Configure the following environment variables:
   ```env
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
   NEXT_PUBLIC_APP_URL=https://your-app-domain.netlify.app
   ```
4. **Deploy**: Automatic deployment with GitHub integration

### **Alternative Deployment Options**
- **Vercel**: Full Next.js support with serverless functions
- **Railway**: PostgreSQL and Next.js hosting
- **Docker**: Containerized deployment for any platform

## 🔮 Future Improvements & Enhancements

### **🎯 Near-Term Enhancements (Next 3-6 months)**
- [ ] **Mobile Application**: React Native app with offline synchronization
- [ ] **Advanced Analytics**: Predictive spending analysis and financial forecasting
- [ ] **Receipt Scanning**: AI-powered receipt parsing and automatic transaction creation
- [ ] **Bank Integration**: Open Banking API integration for automatic transaction import
- [ ] **Notification System**: Email and push notifications for budget alerts and goal milestones
- [ ] **Export/Import**: CSV, PDF, and Excel export functionality
- [ ] **Multi-Currency**: Advanced multi-currency support with real-time exchange rates
- [ ] **Recurring Transactions**: Automated recurring income and expense management

### **📊 Advanced Features (6-12 months)**
- [ ] **Investment Tracking**: Portfolio management and investment account integration
- [ ] **Tax Preparation**: Tax category classification and annual report generation
- [ ] **Bill Reminders**: Automated bill tracking and payment reminders
- [ ] **Subscription Management**: Recurring subscription tracking and cancellation alerts
- [ ] **Financial Health Score**: AI-driven financial health assessment and recommendations
- [ ] **Debt Management**: Debt payoff planning and progress tracking
- [ ] **Financial Planning Tools**: Retirement planning and college savings calculators
- [ ] **Advanced Reporting**: Custom report builder with scheduling capabilities

### **🔧 Technical Improvements**
- [ ] **Performance Optimization**: Database query optimization and caching improvements
- [ ] **Real-time Updates**: WebSocket integration for live collaboration
- [ ] **Progressive Web App**: PWA features for mobile-like experience
- [ ] **Accessibility Enhancement**: WCAG 2.1 AAA compliance improvements
- [ ] **Testing Coverage**: Comprehensive unit, integration, and E2E test suite
- [ ] **API Development**: Public API for third-party integrations
- [ ] **Microservices Architecture**: Service-oriented architecture for scalability
- [ ] **Advanced Security**: Two-factor authentication and advanced audit logging

### **🤝 Collaboration Features**
- [ ] **Family Budgeting Workflow**: Approval workflows for large expenses
- [ ] **Spending Categories**: Per-member spending limits and controls
- [ ] **Financial Goals Collaboration**: Shared savings goals with contribution tracking
- [ ] **Activity Feed**: Real-time family financial activity updates
- [ ] **Permission Granularity**: Advanced role-based permission system
- [ ] **Family Financial Reports**: Automated family financial summaries and insights

### **🌐 Integration & Ecosystem**
- [ ] **Accounting Software**: QuickBooks, Xero, and FreshBooks integration
- [ ] **Payment Platforms**: Stripe, PayPal, and digital wallet integration
- [ ] **Smart Home**: Integration with Alexa and Google Assistant for voice commands
- [ ] **Calendar Integration**: Financial event scheduling and reminders
- [ ] **Social Features**: Anonymized spending comparisons and financial challenges

## 🤝 Contributing

We welcome contributions from the community! Here's how to get involved:

### **Development Setup**
1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Follow the existing code patterns and conventions
4. Ensure TypeScript strict mode compliance
5. Run tests and linting before committing
6. Submit a pull request with a clear description

### **Contribution Guidelines**
- **Code Quality**: Maintain high code quality with proper TypeScript usage
- **Security**: Follow security best practices, especially for financial data
- **Testing**: Include tests for new features and bug fixes
- **Documentation**: Update documentation for new features
- **UI/UX**: Maintain consistent design patterns with existing components

### **Areas for Contribution**
- Bug fixes and performance improvements
- New feature development from the roadmap
- Documentation improvements
- Test coverage expansion
- Accessibility enhancements

## 📄 License

This project is licensed under the **MIT License** - see the [LICENSE](LICENSE) file for complete details.

## 🙏 Acknowledgments

- **Next.js Team** for the outstanding React framework
- **Supabase** for the comprehensive backend-as-a-service platform
- **shadcn/ui** for the beautiful and accessible component library
- **Tailwind CSS** for the utility-first CSS framework
- **Open Source Community** for the amazing ecosystem of tools and libraries

---

**Built with ❤️ for modern family financial management**

