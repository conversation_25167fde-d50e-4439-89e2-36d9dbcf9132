-- Add Missing Schema Columns to Fix TypeScript Type Mismatches
-- This migration adds columns that the application code expects but are missing from the database

-- Add missing columns to accounts table
ALTER TABLE accounts 
ADD COLUMN IF NOT EXISTS institution TEXT,
ADD COLUMN IF NOT EXISTS last_synced TIMESTAMPTZ;

-- Add missing columns to budgets table  
ALTER TABLE budgets
ADD COLUMN IF NOT EXISTS period_start TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS period_end TIMESTAMPTZ;

-- Update existing budgets to populate the new columns from existing start_date/end_date
UPDATE budgets 
SET 
    period_start = start_date,
    period_end = end_date
WHERE period_start IS NULL OR period_end IS NULL;

-- Add missing columns to financial_goals table
ALTER TABLE financial_goals
ADD COLUMN IF NOT EXISTS description TEXT;

-- <PERSON><PERSON> indexes for performance on new columns
CREATE INDEX IF NOT EXISTS idx_accounts_institution ON accounts(institution);
CREATE INDEX IF NOT EXISTS idx_accounts_last_synced ON accounts(last_synced);
CREATE INDEX IF NOT EXISTS idx_budgets_period_start ON budgets(period_start);
CREATE INDEX IF NOT EXISTS idx_budgets_period_end ON budgets(period_end);

-- Comment: This migration adds missing columns that the application code expects:
-- 1. accounts.institution and accounts.last_synced for bank integration features
-- 2. budgets.period_start and budgets.period_end for period-based budget calculations  
-- 3. financial_goals.description for detailed goal descriptions
-- These changes align the database schema with the TypeScript interfaces used in the application.