# Beta Program Backend Integration - Testing Guide

This guide will help you test the complete backend integration for the Budget Tracker beta program.

## 🚀 Quick Start

### 1. Database Setup (Required First)

**Option A: Supabase Dashboard (Recommended)**
1. Go to [Supabase Dashboard](https://supabase.com/dashboard)
2. Find your `budgetdb` project (ID: ddwkbtkbcnbsabepsfsd)
3. If inactive, click "Resume" to reactivate the project
4. Open SQL Editor
5. Copy and paste the contents of `src/beta/migrations/apply-beta-schema.sql`
6. Run the migration script
7. Verify success by checking for `beta_feedback` table

**Option B: Supabase CLI**
```bash
supabase link --project-ref ddwkbtkbcnbsabepsfsd
supabase db push
```

### 2. Environment Configuration

Create `.env.local` from `.env.example`:
```bash
cp .env.example .env.local
```

Update with your Supabase credentials:
```env
NEXT_PUBLIC_SUPABASE_URL=https://ddwkbtkbcnbsabepsfsd.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here
```

### 3. Start Development Server

```bash
npm run dev
```

## 🧪 Testing Scenarios

### Scenario 1: Mock Data Mode (Default)

**Purpose**: Test the beta program with safe mock data

**Steps**:
1. Visit `http://localhost:3000/beta`
2. Sign up for beta access
3. Complete onboarding (choose any finance type)
4. Navigate to dashboard
5. **Check console logs** - should see `🎭 Using MOCK` messages
6. Test all dashboard features:
   - Create accounts, transactions, budgets, goals
   - Submit feedback via the feedback widget
   - Try different finance type dashboards

**Expected Results**:
- All features work with demo data
- Console shows mock service usage
- Data resets on page refresh
- No database connection required

### Scenario 2: Real Database Mode

**Purpose**: Test actual database integration

**Prerequisites**: 
- Database migration completed
- Valid Supabase credentials in `.env.local`

**Steps**:
1. In the dashboard, click your profile dropdown (top right)
2. Toggle the "Data Source" switch to enable "Real Database"
3. **Check console logs** - should see `🔗 Using REAL` messages
4. Test core functionality:
   - User profile loading
   - Feedback submission
   - Finance type switching
   - Onboarding reset

**Expected Results**:
- Console shows real service usage
- Data persists across page refreshes
- Feedback appears in Supabase `beta_feedback` table
- User profile updates in `user_profiles` table

### Scenario 3: Data Mode Toggle

**Purpose**: Test switching between mock and real data

**Steps**:
1. Start in mock mode, create some test data
2. Switch to real mode via toggle
3. Verify data source change in console
4. Switch back to mock mode
5. Verify mock data is restored

**Expected Results**:
- Smooth transitions between modes
- Console logs clearly indicate current mode
- No errors during switching
- Data appropriate to each mode

### Scenario 4: Finance Type Testing

**Purpose**: Test all three finance journeys with real data

**Steps**:
1. Enable real database mode
2. Complete onboarding for "Personal Finance"
3. Explore personal dashboard features
4. Go to Settings → Reset Onboarding
5. Choose "Family Finance" and test
6. Repeat for "Combined Finance"

**Expected Results**:
- Each finance type shows appropriate features
- Navigation adapts to finance type
- Database correctly stores finance type preference
- Onboarding reset works properly

## 🔍 Verification Checklist

### Database Verification

Check your Supabase database for:

```sql
-- Verify beta schema was applied
SELECT column_name FROM information_schema.columns 
WHERE table_name = 'user_profiles' AND column_name LIKE '%beta%';

-- Check beta feedback table
SELECT * FROM beta_feedback ORDER BY created_at DESC LIMIT 5;

-- View beta program analytics
SELECT * FROM beta_program_analytics;

-- Check user profiles with beta fields
SELECT id, email, beta_user, finance_type, onboarding_completed 
FROM user_profiles WHERE beta_user = true;
```

### Console Log Verification

Look for these log patterns:

**Mock Mode**:
```
🎭 Using MOCK Personal Finance Service
🎭 Using MOCK Beta Service - getBetaUser
🎭 Using MOCK AI Insights Service
```

**Real Mode**:
```
🔗 Using REAL Personal Finance Service - getPersonalFinanceData
🔗 Using REAL Beta Service - getBetaUser
🔗 Using REAL AI Insights Service - generateInsights
```

### UI Verification

Check these UI elements:

- [ ] Data mode badge in header shows correct status
- [ ] Settings page has data mode toggle
- [ ] Toggle works in both header dropdown and settings page
- [ ] Finance type badge displays correctly
- [ ] Feedback widget submits successfully

## 🐛 Troubleshooting

### Common Issues

**1. TypeScript Errors**
- **Cause**: Database schema doesn't match beta types yet
- **Solution**: Apply the beta migration first, then restart dev server
- **Temporary**: Use mock mode for development

**2. Database Connection Errors**
- **Cause**: Invalid credentials or inactive project
- **Solution**: Check `.env.local` and reactivate Supabase project
- **Workaround**: Use mock mode until database is ready

**3. Toggle Not Working**
- **Cause**: Context not updating properly
- **Solution**: Check browser console for errors
- **Debug**: Refresh page and try again

**4. Data Not Persisting**
- **Cause**: Still in mock mode or database issues
- **Solution**: Verify real mode is active and database is accessible
- **Check**: Console logs should show "REAL" not "MOCK"

### Debug Commands

```bash
# Check TypeScript issues
npm run type-check

# Check for linting issues
npm run lint

# Build the project
npm run build
```

### Database Debug Queries

```sql
-- Check if beta migration was applied
SELECT EXISTS (
  SELECT FROM information_schema.tables 
  WHERE table_name = 'beta_feedback'
);

-- Test beta user creation
UPDATE user_profiles 
SET beta_user = true, finance_type = 'personal'
WHERE email = '<EMAIL>';

-- View recent feedback
SELECT bf.*, up.email 
FROM beta_feedback bf 
JOIN user_profiles up ON bf.user_id = up.id 
ORDER BY bf.created_at DESC;
```

## 📊 Success Metrics

After successful testing, you should have:

- [ ] ✅ Database migration applied successfully
- [ ] ✅ Both mock and real data modes working
- [ ] ✅ Smooth toggle between data modes
- [ ] ✅ All three finance types functional
- [ ] ✅ Feedback collection working
- [ ] ✅ User preferences persisting
- [ ] ✅ Console logs showing correct service usage
- [ ] ✅ No critical TypeScript errors
- [ ] ✅ Beta program ready for user testing

## 🎯 Next Steps

Once testing is complete:

1. **Deploy to staging** for broader testing
2. **Invite beta users** to test the program
3. **Monitor feedback** through the database
4. **Iterate** based on user feedback
5. **Prepare for production** launch

The beta program is now fully integrated with real database support and ready for comprehensive user testing!
