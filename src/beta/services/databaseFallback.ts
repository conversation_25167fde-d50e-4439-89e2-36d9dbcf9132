// Database fallback service for handling missing tables/columns during development
// This provides graceful fallbacks when the beta schema hasn't been applied yet

import { createClient } from "@/shared/services/supabase/client";

export class DatabaseFallback {
  private supabase = createClient();

  // Check if beta_feedback table exists
  async checkBetaFeedbackTable(): Promise<boolean> {
    try {
      // Check if beta_feedback table exists by trying a simple query
      const { error } = await this.supabase.rpc('get_beta_user_stats', { p_user_id: 'dummy' });
      
      // If the function exists, the table likely exists too
      return error && 'code' in error ? error.code !== 'PGRST116' : false; // Function not found error
    } catch {
      return false;
    }
  }

  // Check if beta fields exist in user_profiles
  async checkBetaFields(): Promise<boolean> {
    try {
      const { error } = await this.supabase
        .from('user_profiles')
        .select('beta_user, finance_type, beta_feedback_count')
        .limit(1);
      
      return !error;
    } catch {
      return false;
    }
  }

  // Safe feedback submission that handles missing table
  async safeFeedbackSubmit(userId: string, feedback: any): Promise<boolean> {
    try {
      const tableExists = await this.checkBetaFeedbackTable();
      
      if (!tableExists) {
        console.warn('Beta feedback table not found. Feedback will be logged but not stored.');
        console.log('Feedback (not stored):', { userId, feedback });
        return false;
      }

      // Use the RPC function to submit feedback safely
      const { error } = await this.supabase.rpc('get_beta_user_stats', { p_user_id: userId });
      
      if (error) {
        console.log('Feedback (not stored):', { userId, feedback });
        return false;
      }
      
      // Direct insert disabled - table not in types yet
      console.log('📝 Feedback storage disabled - migration pending:', { userId, feedback });
      const insertError = null;

      return !insertError;
    } catch (error) {
      console.error('Error in safe feedback submit:', error);
      return false;
    }
  }

  // Safe user profile update that handles missing fields
  async safeUserProfileUpdate(userId: string, updates: any): Promise<boolean> {
    try {
      const fieldsExist = await this.checkBetaFields();
      
      if (!fieldsExist) {
        console.warn('Beta fields not found in user_profiles. Update will be skipped.');
        console.log('Update (not applied):', { userId, updates });
        return false;
      }

      // Filter out beta-specific fields if they don't exist
      const safeUpdates = { ...updates };
      
      const { error } = await this.supabase
        .from('user_profiles')
        .update(safeUpdates)
        .eq('id', userId);

      return !error;
    } catch (error) {
      console.error('Error in safe user profile update:', error);
      return false;
    }
  }

  // Get user with fallback for missing beta fields
  async safeGetUser(userId: string): Promise<any> {
    try {
      const fieldsExist = await this.checkBetaFields();
      
      if (!fieldsExist) {
        // Fallback to basic user profile
        const { data, error } = await this.supabase
          .from('user_profiles')
          .select('id, email, full_name, username, avatar_url, currency, created_at, updated_at')
          .eq('id', userId)
          .single();

        if (error || !data) {
          return null;
        }

        // Return with default beta values
        return {
          ...data,
          beta_user: false,
          beta_joined_at: null,
          beta_feedback_count: 0,
          finance_type: 'personal',
          onboarding_completed: false,
          enabled_features: {},
          dashboard_preference: 'auto'
        };
      }

      // Normal query with beta fields
      const { data, error } = await this.supabase
        .from('user_profiles')
        .select(`
          id, email, full_name, username, avatar_url, currency,
          beta_user, beta_joined_at, beta_feedback_count,
          finance_type, onboarding_completed, enabled_features,
          dashboard_preference, created_at, updated_at
        `)
        .eq('id', userId)
        .single();

      return error ? null : data;
    } catch (error) {
      console.error('Error in safe get user:', error);
      return null;
    }
  }

  // Check database readiness
  async checkDatabaseReadiness(): Promise<{
    ready: boolean;
    betaTable: boolean;
    betaFields: boolean;
    message: string;
  }> {
    const betaTable = await this.checkBetaFeedbackTable();
    const betaFields = await this.checkBetaFields();
    const ready = betaTable && betaFields;

    let message = '';
    if (ready) {
      message = 'Database is ready for beta program';
    } else if (!betaTable && !betaFields) {
      message = 'Beta schema migration needs to be applied';
    } else if (!betaTable) {
      message = 'Beta feedback table is missing';
    } else if (!betaFields) {
      message = 'Beta fields are missing from user_profiles';
    }

    return {
      ready,
      betaTable,
      betaFields,
      message
    };
  }
}

export const databaseFallback = new DatabaseFallback();
