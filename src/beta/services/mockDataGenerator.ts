// Mock data generator for testing purposes
// This generates realistic financial data for testing the beta features

import { PersonalFinanceData } from '../dashboards/personal/types/personal';
import { FamilyFinanceData } from '../dashboards/family/types/family';
import { CombinedFinanceData } from '../dashboards/combined/types/combined';

// Helper function to generate random dates
const getRandomDate = (daysBack: number = 30): string => {
  const date = new Date();
  date.setDate(date.getDate() - Math.floor(Math.random() * daysBack));
  return date.toISOString().split('T')[0];
};

// Helper function to generate random amounts
const getRandomAmount = (min: number = 10, max: number = 1000): number => {
  return Math.floor(Math.random() * (max - min) + min);
};

// Generate mock personal finance data
export const generateMockPersonalData = (): PersonalFinanceData => {
  const mockUserId = 'mock-user-1';
  
  const mockAccounts = [
    {
      id: 'acc-1',
      user_id: mockUserId,
      name: 'Main Checking',
      type: 'checking' as const,
      balance: 2500.75,
      currency: 'USD',
      institution: 'Chase Bank',
      is_active: true,
      created_at: '2024-01-15T00:00:00Z',
      updated_at: '2024-01-15T00:00:00Z'
    },
    {
      id: 'acc-2',
      user_id: mockUserId,
      name: 'Savings Account',
      type: 'savings' as const,
      balance: 15000.00,
      currency: 'USD',
      institution: 'Chase Bank',
      is_active: true,
      created_at: '2024-01-15T00:00:00Z',
      updated_at: '2024-01-15T00:00:00Z'
    },
    {
      id: 'acc-3',
      user_id: mockUserId,
      name: 'Credit Card',
      type: 'credit_card' as const,
      balance: -850.25,
      currency: 'USD',
      institution: 'Capital One',
      is_active: true,
      created_at: '2024-01-15T00:00:00Z',
      updated_at: '2024-01-15T00:00:00Z'
    }
  ];

  const mockTransactions = [
    {
      id: 'txn-1',
      user_id: mockUserId,
      account_id: 'acc-1',
      category_id: 'cat-1',
      amount: -85.50,
      description: 'Grocery Store',
      type: 'expense' as const,
      date: getRandomDate(7),
      currency: 'USD',
      created_at: '2024-01-15T00:00:00Z',
      updated_at: '2024-01-15T00:00:00Z',
      category: { id: 'cat-1', name: 'Groceries', icon: '🛒', color: '#10B981', type: 'expense', created_at: '2024-01-15T00:00:00Z', updated_at: '2024-01-15T00:00:00Z', user_id: mockUserId }
    },
    {
      id: 'txn-2',
      user_id: mockUserId,
      account_id: 'acc-1',
      category_id: 'cat-2',
      amount: 3200.00,
      description: 'Salary Deposit',
      type: 'income' as const,
      date: getRandomDate(3),
      currency: 'USD',
      created_at: '2024-01-15T00:00:00Z',
      updated_at: '2024-01-15T00:00:00Z',
      category: { id: 'cat-2', name: 'Salary', icon: '💰', color: '#059669', type: 'income', created_at: '2024-01-15T00:00:00Z', updated_at: '2024-01-15T00:00:00Z', user_id: mockUserId }
    },
    {
      id: 'txn-3',
      user_id: mockUserId,
      account_id: 'acc-3',
      category_id: 'cat-3',
      amount: -45.00,
      description: 'Gas Station',
      type: 'expense' as const,
      date: getRandomDate(5),
      currency: 'USD',
      created_at: '2024-01-15T00:00:00Z',
      updated_at: '2024-01-15T00:00:00Z',
      category: { id: 'cat-3', name: 'Transportation', icon: '⛽', color: '#F59E0B', type: 'expense', created_at: '2024-01-15T00:00:00Z', updated_at: '2024-01-15T00:00:00Z', user_id: mockUserId }
    },
    {
      id: 'txn-4',
      user_id: mockUserId,
      account_id: 'acc-1',
      category_id: 'cat-4',
      amount: -1200.00,
      description: 'Rent Payment',
      type: 'expense' as const,
      date: getRandomDate(1),
      currency: 'USD',
      created_at: '2024-01-15T00:00:00Z',
      updated_at: '2024-01-15T00:00:00Z',
      category: { id: 'cat-4', name: 'Housing', icon: '🏠', color: '#EF4444', type: 'expense', created_at: '2024-01-15T00:00:00Z', updated_at: '2024-01-15T00:00:00Z', user_id: mockUserId }
    },
    {
      id: 'txn-5',
      user_id: mockUserId,
      account_id: 'acc-1',
      category_id: 'cat-5',
      amount: -25.99,
      description: 'Netflix Subscription',
      type: 'expense' as const,
      date: getRandomDate(10),
      currency: 'USD',
      created_at: '2024-01-15T00:00:00Z',
      updated_at: '2024-01-15T00:00:00Z',
      category: { id: 'cat-5', name: 'Entertainment', icon: '🎬', color: '#8B5CF6', type: 'expense', created_at: '2024-01-15T00:00:00Z', updated_at: '2024-01-15T00:00:00Z', user_id: mockUserId }
    }
  ];

  const mockBudgets = [
    {
      id: 'budget-1',
      user_id: mockUserId,
      name: 'Monthly Groceries',
      category_id: 'cat-1',
      amount: 400.00,
      period: 'monthly' as const,
      period_start: '2024-01-01',
      period_end: '2024-01-31',
      spent: 285.50,
      remaining: 114.50,
      percentage_used: 71.4,
      status: 'on_track' as const,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-15T00:00:00Z',
      category: { id: 'cat-1', name: 'Groceries', icon: '🛒', color: '#10B981', type: 'expense', created_at: '2024-01-15T00:00:00Z', updated_at: '2024-01-15T00:00:00Z', user_id: mockUserId }
    },
    {
      id: 'budget-2',
      user_id: mockUserId,
      name: 'Transportation',
      category_id: 'cat-3',
      amount: 200.00,
      period: 'monthly' as const,
      period_start: '2024-01-01',
      period_end: '2024-01-31',
      spent: 165.00,
      remaining: 35.00,
      percentage_used: 82.5,
      status: 'warning' as const,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-15T00:00:00Z',
      category: { id: 'cat-3', name: 'Transportation', icon: '⛽', color: '#F59E0B', type: 'expense', created_at: '2024-01-15T00:00:00Z', updated_at: '2024-01-15T00:00:00Z', user_id: mockUserId }
    },
    {
      id: 'budget-3',
      user_id: mockUserId,
      name: 'Entertainment',
      category_id: 'cat-5',
      amount: 100.00,
      period: 'monthly' as const,
      period_start: '2024-01-01',
      period_end: '2024-01-31',
      spent: 125.99,
      remaining: -25.99,
      percentage_used: 126.0,
      status: 'exceeded' as const,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-15T00:00:00Z',
      category: { id: 'cat-5', name: 'Entertainment', icon: '🎬', color: '#8B5CF6', type: 'expense', created_at: '2024-01-15T00:00:00Z', updated_at: '2024-01-15T00:00:00Z', user_id: mockUserId }
    }
  ];

  const mockGoals = [
    {
      id: 'goal-1',
      user_id: mockUserId,
      name: 'Emergency Fund',
      description: 'Build 6 months of expenses',
      target_amount: 20000.00,
      current_amount: 15000.00,
      target_date: '2024-12-31',
      status: 'active' as const,
      progress_percentage: 75.0,
      progress: 75.0,
      monthly_target: 500.00,
      days_remaining: 320,
      currency: 'USD',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-15T00:00:00Z'
    },
    {
      id: 'goal-2',
      user_id: mockUserId,
      name: 'Vacation Fund',
      description: 'Save for Europe trip',
      target_amount: 5000.00,
      current_amount: 1200.00,
      target_date: '2024-08-01',
      status: 'active' as const,
      progress_percentage: 24.0,
      progress: 24.0,
      monthly_target: 760.00,
      days_remaining: 180,
      currency: 'USD',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-15T00:00:00Z'
    }
  ];

  const mockAnalytics = {
    overview: {
      total_balance: 16650.50,
      total_income: 3200.00,
      total_expenses: 1356.49,
      net_income: 1843.51,
      savings_rate: 57.6,
      account_count: 3,
      active_budgets: 3,
      active_goals: 2
    },
    spending_by_category: [
      { category_name: 'Housing', amount: 1200.00, percentage: 88.5, color: '#EF4444' },
      { category_name: 'Groceries', amount: 85.50, percentage: 6.3, color: '#10B981' },
      { category_name: 'Transportation', amount: 45.00, percentage: 3.3, color: '#F59E0B' },
      { category_name: 'Entertainment', amount: 25.99, percentage: 1.9, color: '#8B5CF6' }
    ],
    income_vs_expenses: [
      { month: 'Jan 2024', income: 3200.00, expenses: 1356.49, net: 1843.51, savings_rate: 57.6 }
    ],
    budget_performance: [
      { budget_name: 'Monthly Groceries', budgeted: 400.00, spent: 285.50, variance: 114.50, status: 'on_track' as const },
      { budget_name: 'Transportation', budgeted: 200.00, spent: 165.00, variance: 35.00, status: 'warning' as const },
      { budget_name: 'Entertainment', budgeted: 100.00, spent: 125.99, variance: -25.99, status: 'exceeded' as const }
    ],
    goal_progress: [
      { goal_name: 'Emergency Fund', target: 20000.00, current: 15000.00, progress_percentage: 75.0, projected_completion: '2024-08-15' },
      { goal_name: 'Vacation Fund', target: 5000.00, current: 1200.00, progress_percentage: 24.0, projected_completion: '2025-06-01' }
    ],
    monthly_trends: [
      { month: 'Dec 2023', total_income: 3200.00, total_expenses: 1450.00, savings: 1750.00, savings_rate: 54.7 },
      { month: 'Jan 2024', total_income: 3200.00, total_expenses: 1356.49, savings: 1843.51, savings_rate: 57.6 }
    ]
  };

  return {
    accounts: mockAccounts,
    transactions: mockTransactions,
    budgets: mockBudgets,
    goals: mockGoals,
    analytics: mockAnalytics
  };
};

// Generate mock family finance data
export const generateMockFamilyData = (): FamilyFinanceData => {
  const mockUserId = 'mock-user-1';
  
  const mockGroups = [
    {
      id: 'group-1',
      name: 'Smith Family',
      description: 'Our family finances',
      owner_user_id: 'user-1',
      member_count: 3,
      balance: 8500.00,
      monthly_income: 6500.00,
      monthly_expenses: 4200.00,
      currency: 'USD',
      is_admin: true,
      is_member: true,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-15T00:00:00Z'
    }
  ];

  const mockAccounts = [
    {
      id: 'fam-acc-1',
      family_group_id: 'group-1',
      name: 'Family Checking',
      type: 'checking',
      balance: 3500.00,
      currency: 'USD',
      institution: 'Wells Fargo',
      is_active: true,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-15T00:00:00Z',
      created_by_user_id: 'user-1',
      permissions: { can_view: [], can_edit: [], can_transact: [] }
    },
    {
      id: 'fam-acc-2',
      family_group_id: 'group-1',
      name: 'Family Savings',
      type: 'savings',
      balance: 5000.00,
      currency: 'USD',
      institution: 'Wells Fargo',
      is_active: true,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-15T00:00:00Z',
      created_by_user_id: 'user-1',
      permissions: { can_view: [], can_edit: [], can_transact: [] }
    }
  ];

  const mockTransactions = [
    {
      id: 'fam-txn-1',
      family_group_id: 'group-1',
      account_id: 'fam-acc-1',
      amount: -150.00,
      description: 'Family Grocery Shopping',
      type: 'expense' as const,
      date: getRandomDate(3),
      currency: 'USD',
      created_by: 'user-1',
      created_at: '2024-01-15T00:00:00Z',
      updated_at: '2024-01-15T00:00:00Z',
      category: { id: 'cat-1', name: 'Groceries', icon: '🛒', color: '#10B981', type: 'expense', created_at: '2024-01-15T00:00:00Z', updated_at: '2024-01-15T00:00:00Z', user_id: mockUserId },
      created_by_user: { full_name: 'John Smith', username: 'johnsmith' }
    },
    {
      id: 'fam-txn-2',
      family_group_id: 'group-1',
      account_id: 'fam-acc-1',
      amount: 6500.00,
      description: 'Combined Salaries',
      type: 'income' as const,
      date: getRandomDate(1),
      currency: 'USD',
      created_by: 'user-1',
      created_at: '2024-01-15T00:00:00Z',
      updated_at: '2024-01-15T00:00:00Z',
      category: { id: 'cat-2', name: 'Salary', icon: '💰', color: '#059669', type: 'income', created_at: '2024-01-15T00:00:00Z', updated_at: '2024-01-15T00:00:00Z', user_id: mockUserId },
      created_by_user: { full_name: 'John Smith', username: 'johnsmith' }
    }
  ];

  const mockBudgets = [
    {
      id: 'fam-budget-1',
      family_group_id: 'group-1',
      name: 'Family Groceries',
      amount: 600.00,
      period: 'monthly' as const,
      period_start: '2024-01-01',
      period_end: '2024-01-31',
      spent: 450.00,
      remaining: 150.00,
      percentage_used: 75.0,
      status: 'on_track' as const,
      created_by: 'user-1',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-15T00:00:00Z',
      contributors: []
    }
  ];

  const mockGoals = [
    {
      id: 'fam-goal-1',
      family_group_id: 'group-1',
      name: 'Family Vacation',
      description: 'Disney World trip',
      target_amount: 8000.00,
      current_amount: 2500.00,
      target_date: '2024-07-01',
      status: 'active' as const,
      progress_percentage: 31.25,
      monthly_target: 1100.00,
      days_remaining: 150,
      currency: 'USD',
      created_by: 'user-1',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-15T00:00:00Z',
      contributors: []
    }
  ];

  const mockAnalytics = {
    overview: {
      total_balance: 8500.00,
      total_income: 6500.00,
      total_expenses: 4200.00,
      net_income: 2300.00,
      savings_rate: 35.4,
      group_count: 1,
      account_count: 2,
      active_budgets: 1,
      active_goals: 1,
      member_count: 3
    },
    spending_by_category: [],
    income_vs_expenses: [],
    budget_performance: [],
    goal_progress: [],
    member_contributions: [],
    monthly_trends: []
  };

  return {
    groups: mockGroups,
    accounts: mockAccounts,
    transactions: mockTransactions,
    budgets: mockBudgets,
    goals: mockGoals,
    analytics: mockAnalytics
  };
};

// Generate mock combined finance data
export const generateMockCombinedData = (): CombinedFinanceData => {
  const personalData = generateMockPersonalData();
  const familyData = generateMockFamilyData();

  return {
    personal: personalData,
    family: {
      groups: familyData.groups,
      total_balance: 8500.00,
      monthly_income: 6500.00,
      monthly_expenses: 4200.00,
      active_budgets: 1,
      active_goals: 1,
      recent_transactions: familyData.transactions.slice(0, 5).map(t => ({
        id: t.id,
        group_id: t.family_group_id,
        group_name: 'Smith Family',
        amount: t.amount,
        description: t.description,
        type: t.type,
        category: t.category?.name || 'Other',
        date: t.date,
        created_by: t.created_by,
        currency: t.currency
      }))
    },
    unified: {
      total_balance: 25150.50, // Personal + Family
      total_monthly_income: 5500.00, // Personal + Family income
      total_monthly_expenses: 3306.49, // Personal + Family expenses
      net_cash_flow: 4143.51, // Personal + Family net
      savings_rate: 46.5, // Combined savings rate
      personal_vs_family: {
        personal_balance: 16650.50,
        family_balance: 8500.00,
        personal_spending: 1356.49,
        family_spending: 1950.00,
        personal_income: 3200.00,
        family_income: 2300.00
      },
      goal_progress: [
        {
          id: 'goal-1',
          name: 'Emergency Fund',
          type: 'personal' as const,
          target_amount: 20000.00,
          current_amount: 15000.00,
          progress: 75.0,
          days_remaining: 120,
          monthly_required: 416.67,
          currency: 'USD'
        }
      ],
      spending_comparison: [
        {
          category: 'Groceries',
          personal_amount: 285.50,
          family_amount: 450.00,
          total_amount: 735.50,
          percentage: 35.2,
          color: '#10B981'
        }
      ],
      cash_flow_trends: [
        {
          month: 'Jan 2024',
          personal_income: 3200.00,
          personal_expenses: 1356.49,
          family_income: 2300.00,
          family_expenses: 1950.00,
          net_flow: 2193.51
        }
      ]
    },
    user_preferences: {
      show_amounts: true,
      preferred_currency: 'USD',
      default_view: 'unified',
      analytics_period: 'month' as const
    }
  };
};
