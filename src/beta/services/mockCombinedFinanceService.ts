// Mock Combined Finance Service for testing purposes
// This service returns mock data instead of making real database calls

import { CombinedFinanceData, CombinedFinanceInsight } from '../dashboards/combined/types/combined';
import { generateMockCombinedData } from './mockDataGenerator';

export class MockCombinedFinanceService {
  private mockData: CombinedFinanceData;

  constructor() {
    this.mockData = generateMockCombinedData();
  }

  // Simulate async operations with small delays
  private async delay(ms: number = 300): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async getFamilyFinanceData(userId: string) {
    await this.delay();
    console.log('🎭 Using MOCK Family Finance Data for user:', userId);
    return this.mockData.family;
  }

  async getCombinedFinanceData(userId: string): Promise<CombinedFinanceData> {
    await this.delay();
    console.log('🎭 Using MOCK Combined Finance Data for user:', userId);
    return this.mockData;
  }

  async getUnifiedAnalytics(userId: string) {
    await this.delay();
    console.log('🎭 Using MOCK Unified Analytics for user:', userId);
    return this.mockData.unified;
  }

  async getCombinedInsights(userId: string): Promise<CombinedFinanceInsight[]> {
    await this.delay();
    console.log('🎭 Using MOCK Combined Insights for user:', userId);
    
    return [
      {
        id: 'combined-insight-1',
        type: 'cross_account',
        priority: 'medium',
        title: 'Balance Optimization Opportunity',
        description: 'Consider transferring some personal funds to family accounts for better diversification.',
        action_required: true,
        action_text: 'Review allocation',
        data_points: {
          personal_balance: 16650.50,
          family_balance: 8500.00,
          recommendation: 'transfer'
        },
        created_at: new Date().toISOString()
      },
      {
        id: 'combined-insight-2',
        type: 'goal_alignment',
        priority: 'low',
        title: 'Goal Alignment Score',
        description: 'Your personal and family goals are well aligned with an 85% alignment score.',
        action_required: false,
        data_points: {
          alignment_score: 85,
          total_goals: 3,
          aligned_goals: 1
        },
        created_at: new Date().toISOString()
      },
      {
        id: 'combined-insight-3',
        type: 'cash_flow',
        priority: 'high',
        title: 'Excellent Combined Savings Rate',
        description: 'Your combined savings rate of 46.5% is outstanding! Consider diversifying investments.',
        action_required: true,
        action_text: 'Explore investment options',
        data_points: {
          savings_rate: 46.5,
          excess_funds: 4143.51
        },
        created_at: new Date().toISOString()
      }
    ];
  }

  async getUserPreferences(userId: string) {
    await this.delay();
    console.log('🎭 Using MOCK User Preferences for user:', userId);
    return this.mockData.user_preferences;
  }

  async updateUserPreferences(userId: string, preferences: any) {
    await this.delay();
    console.log('🎭 MOCK: Updating user preferences for user:', userId);
    this.mockData.user_preferences = {
      ...this.mockData.user_preferences,
      ...preferences
    };
    return this.mockData.user_preferences;
  }

  async switchFinanceMode(userId: string, mode: 'personal' | 'family' | 'combined') {
    await this.delay();
    console.log('🎭 MOCK: Switching finance mode to:', mode);
    // Note: finance_mode would need to be stored separately or added to UserPreferences interface
    return this.mockData.user_preferences;
  }

  // Cross-account operations
  async transferBetweenAccounts(userId: string, fromAccountId: string, toAccountId: string, amount: number) {
    await this.delay();
    console.log('🎭 MOCK: Transferring between accounts:', { fromAccountId, toAccountId, amount });
    
    // Simulate creating transfer transactions
    const transferOut = {
      id: `mock-transfer-out-${Date.now()}`,
      account_id: fromAccountId,
      amount: -amount,
      description: `Transfer to ${toAccountId}`,
      type: 'transfer' as const,
      date: new Date().toISOString().split('T')[0],
      currency: 'USD',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      category: { id: 'cat-transfer', name: 'Transfer', icon: '↔️', color: '#6B7280', type: 'transfer' }
    };

    const transferIn = {
      id: `mock-transfer-in-${Date.now()}`,
      account_id: toAccountId,
      amount: amount,
      description: `Transfer from ${fromAccountId}`,
      type: 'transfer' as const,
      date: new Date().toISOString().split('T')[0],
      currency: 'USD',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      category: { id: 'cat-transfer', name: 'Transfer', icon: '↔️', color: '#6B7280', type: 'transfer' }
    };

    return {
      transferOut,
      transferIn,
      success: true,
      message: 'Transfer completed successfully'
    };
  }

  async getAccountSummary(userId: string) {
    await this.delay();
    console.log('🎭 Using MOCK Account Summary for user:', userId);
    
    return {
      personal_accounts: this.mockData.personal.accounts.length,
      family_accounts: this.mockData.family.recent_transactions.length > 0 ? 2 : 0,
      total_balance: this.mockData.unified.total_balance,
      personal_balance: this.mockData.unified.personal_vs_family.personal_balance,
      family_balance: this.mockData.unified.personal_vs_family.family_balance,
      net_worth: this.mockData.unified.total_balance,
      monthly_cash_flow: this.mockData.unified.net_cash_flow,
      savings_rate: this.mockData.unified.savings_rate
    };
  }

  async getGoalAlignment(userId: string) {
    await this.delay();
    console.log('🎭 Using MOCK Goal Alignment for user:', userId);
    // Return mock goal alignment data
    return {
      aligned_goals: 1,
      conflicting_goals: 0,
      total_goals: 3,
      alignment_score: 85
    };
  }

  async getCrossAccountInsights(userId: string) {
    await this.delay();
    console.log('🎭 Using MOCK Cross Account Insights for user:', userId);
    
    return [
      {
        type: 'balance_distribution',
        title: 'Account Balance Distribution',
        description: 'Your personal accounts hold 66.2% of your total balance',
        recommendation: 'Consider balancing between personal and family accounts',
        impact: 'medium',
        data: {
          personal_percentage: (this.mockData.unified.personal_vs_family.personal_balance / this.mockData.unified.total_balance) * 100,
          family_percentage: (this.mockData.unified.personal_vs_family.family_balance / this.mockData.unified.total_balance) * 100
        }
      },
      {
        type: 'cash_flow_optimization',
        title: 'Positive Cash Flow',
        description: `Your combined monthly cash flow is $${this.mockData.unified.net_cash_flow.toFixed(2)}`,
        recommendation: 'Excellent! Consider increasing investment contributions',
        impact: 'low',
        data: {
          net_cash_flow: this.mockData.unified.net_cash_flow,
          savings_rate: this.mockData.unified.savings_rate
        }
      }
    ];
  }

  async getFinancialHealthScore(userId: string) {
    await this.delay();
    console.log('🎭 Using MOCK Financial Health Score for user:', userId);
    
    return {
      overall_score: 85,
      categories: {
        savings_rate: { score: 95, weight: 0.3 },
        debt_to_income: { score: 80, weight: 0.25 },
        emergency_fund: { score: 90, weight: 0.2 },
        goal_progress: { score: 75, weight: 0.15 },
        diversification: { score: 70, weight: 0.1 }
      },
      recommendations: [
        'Continue maintaining your excellent savings rate',
        'Consider diversifying your investment portfolio',
        'Your emergency fund is well-funded'
      ],
      trend: 'improving',
      last_updated: new Date().toISOString()
    };
  }
}

export const mockCombinedFinanceService = new MockCombinedFinanceService();
