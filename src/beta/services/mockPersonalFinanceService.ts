// Mock Personal Finance Service for testing purposes
// This service returns mock data instead of making real database calls

import { PersonalFinanceData, PersonalFinanceInsight } from '../dashboards/personal/types/personal';
import { generateMockPersonalData } from './mockDataGenerator';

export class MockPersonalFinanceService {
  private mockData: PersonalFinanceData;

  constructor() {
    this.mockData = generateMockPersonalData();
  }

  // Simulate async operations with small delays
  private async delay(ms: number = 300): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async getPersonalFinanceData(userId: string): Promise<PersonalFinanceData> {
    await this.delay();
    console.log('🎭 Using MOCK Personal Finance Data for user:', userId);
    return this.mockData;
  }

  async getPersonalInsights(userId: string): Promise<PersonalFinanceInsight[]> {
    await this.delay();
    console.log('🎭 Using MOCK Personal Finance Insights for user:', userId);
    
    return [
      {
        id: 'insight-1',
        type: 'budget_warning',
        priority: 'high',
        title: 'Entertainment Budget Exceeded',
        description: 'You\'ve exceeded your entertainment budget by $25.99 this month.',
        action_required: true,
        related_data: {
          budget_id: 'budget-3',
          amount: 25.99
        },
        created_at: new Date().toISOString()
      },
      {
        id: 'insight-2',
        type: 'goal_milestone',
        priority: 'medium',
        title: 'Emergency Fund Progress',
        description: 'You\'re 75% of the way to your emergency fund goal. Great progress!',
        action_required: false,
        related_data: {
          goal_id: 'goal-1'
        },
        created_at: new Date().toISOString()
      },
      {
        id: 'insight-3',
        type: 'trend_analysis',
        priority: 'low',
        title: 'High Savings Rate',
        description: 'Your savings rate of 57.6% is excellent! Consider investing excess savings.',
        action_required: true,
        related_data: {
          amount: 1843.51
        },
        created_at: new Date().toISOString()
      }
    ];
  }

  async getAccounts(userId: string) {
    await this.delay();
    console.log('🎭 Using MOCK Accounts for user:', userId);
    return this.mockData.accounts;
  }

  async getAllTransactions(userId: string) {
    await this.delay();
    console.log('🎭 Using MOCK All Transactions for user:', userId);
    return this.mockData.transactions;
  }

  async getTransactions(userId: string, limit: number = 20) {
    await this.delay();
    console.log('🎭 Using MOCK Transactions for user:', userId);
    return this.mockData.transactions.slice(0, limit);
  }

  async getRecentTransactions(userId: string, limit: number = 20) {
    return this.getTransactions(userId, limit);
  }

  async getTransactionsByDateRange(userId: string, startDate: string, endDate: string) {
    await this.delay();
    console.log('🎭 Using MOCK Transactions by date range for user:', userId);
    return this.mockData.transactions.filter(t => t.date >= startDate && t.date <= endDate);
  }

  async getBudgets(userId: string) {
    await this.delay();
    console.log('🎭 Using MOCK Budgets for user:', userId);
    return this.mockData.budgets;
  }

  async getGoals(userId: string) {
    await this.delay();
    console.log('🎭 Using MOCK Goals for user:', userId);
    return this.mockData.goals;
  }

  async getAnalytics(userId: string) {
    await this.delay();
    console.log('🎭 Using MOCK Analytics for user:', userId);
    return this.mockData.analytics;
  }

  async getCategories(userId: string, type?: 'income' | 'expense') {
    await this.delay();
    console.log('🎭 Using MOCK Categories for user:', userId);

    // Mock categories data with full PersonalCategory structure
    const mockCategories = [
      {
        id: 'cat-1',
        name: 'Food & Dining',
        icon: '🍽️',
        color: '#F59E0B',
        type: 'expense',
        parent_id: null,
        is_system: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        user_id: userId,
        family_group_id: null,
        created_by_user_id: userId
      },
      {
        id: 'cat-2',
        name: 'Transportation',
        icon: '🚗',
        color: '#3B82F6',
        type: 'expense',
        parent_id: null,
        is_system: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        user_id: userId,
        family_group_id: null,
        created_by_user_id: userId
      },
      {
        id: 'cat-3',
        name: 'Shopping',
        icon: '🛍️',
        color: '#8B5CF6',
        type: 'expense',
        parent_id: null,
        is_system: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        user_id: userId,
        family_group_id: null,
        created_by_user_id: userId
      },
      {
        id: 'cat-4',
        name: 'Entertainment',
        icon: '🎬',
        color: '#EF4444',
        type: 'expense',
        parent_id: null,
        is_system: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        user_id: userId,
        family_group_id: null,
        created_by_user_id: userId
      },
      {
        id: 'cat-5',
        name: 'Bills & Utilities',
        icon: '⚡',
        color: '#F97316',
        type: 'expense',
        parent_id: null,
        is_system: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        user_id: userId,
        family_group_id: null,
        created_by_user_id: userId
      },
      {
        id: 'cat-6',
        name: 'Healthcare',
        icon: '🏥',
        color: '#10B981',
        type: 'expense',
        parent_id: null,
        is_system: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        user_id: userId,
        family_group_id: null,
        created_by_user_id: userId
      },
      {
        id: 'cat-7',
        name: 'Salary',
        icon: '💼',
        color: '#059669',
        type: 'income',
        parent_id: null,
        is_system: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        user_id: userId,
        family_group_id: null,
        created_by_user_id: userId
      },
      {
        id: 'cat-8',
        name: 'Freelance',
        icon: '💻',
        color: '#0D9488',
        type: 'income',
        parent_id: null,
        is_system: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        user_id: userId,
        family_group_id: null,
        created_by_user_id: userId
      },
      {
        id: 'cat-9',
        name: 'Investments',
        icon: '📈',
        color: '#7C3AED',
        type: 'income',
        parent_id: null,
        is_system: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        user_id: userId,
        family_group_id: null,
        created_by_user_id: userId
      },
    ];

    if (type) {
      return mockCategories.filter(cat => cat.type === type);
    }
    return mockCategories;
  }

  // Create methods (simulate success)
  async createAccount(userId: string, accountData: any) {
    await this.delay();
    console.log('🎭 MOCK: Creating account for user:', userId);
    const newAccount = {
      id: `mock-acc-${Date.now()}`,
      ...accountData,
      balance: accountData.balance || 0,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
    this.mockData.accounts.push(newAccount);
    return newAccount;
  }

  async createTransaction(userId: string, transactionData: any) {
    await this.delay();
    console.log('🎭 MOCK: Creating transaction for user:', userId);
    const newTransaction = {
      id: `mock-txn-${Date.now()}`,
      ...transactionData,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      category: { id: 'cat-1', name: 'Other', icon: '📝', color: '#6B7280', type: transactionData.type }
    };
    this.mockData.transactions.unshift(newTransaction);
    return newTransaction;
  }

  async createBudget(userId: string, budgetData: any) {
    await this.delay();
    console.log('🎭 MOCK: Creating budget for user:', userId);
    const newBudget = {
      id: `mock-budget-${Date.now()}`,
      ...budgetData,
      spent: 0,
      remaining: budgetData.amount,
      percentage_used: 0,
      status: 'on_track' as const,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      category: { id: 'cat-1', name: 'Other', icon: '📝', color: '#6B7280', type: 'expense' }
    };
    this.mockData.budgets.push(newBudget);
    return newBudget;
  }

  async createGoal(userId: string, goalData: any) {
    await this.delay();
    console.log('🎭 MOCK: Creating goal for user:', userId);
    const newGoal = {
      id: `mock-goal-${Date.now()}`,
      ...goalData,
      current_amount: 0,
      progress_percentage: 0,
      monthly_target: goalData.target_amount / 12,
      status: 'active' as const,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
    this.mockData.goals.push(newGoal);
    return newGoal;
  }

  // Update methods (simulate success)
  async updateAccount(accountId: string, updates: any) {
    await this.delay();
    console.log('🎭 MOCK: Updating account:', accountId);
    const accountIndex = this.mockData.accounts.findIndex(a => a.id === accountId);
    if (accountIndex !== -1) {
      this.mockData.accounts[accountIndex] = {
        ...this.mockData.accounts[accountIndex],
        ...updates,
        updated_at: new Date().toISOString()
      };
      return this.mockData.accounts[accountIndex];
    }
    throw new Error('Account not found');
  }

  async updateTransaction(transactionId: string, updates: any) {
    await this.delay();
    console.log('🎭 MOCK: Updating transaction:', transactionId);
    const transactionIndex = this.mockData.transactions.findIndex(t => t.id === transactionId);
    if (transactionIndex !== -1) {
      this.mockData.transactions[transactionIndex] = {
        ...this.mockData.transactions[transactionIndex],
        ...updates,
        updated_at: new Date().toISOString()
      };
      return this.mockData.transactions[transactionIndex];
    }
    throw new Error('Transaction not found');
  }

  async updateBudget(budgetId: string, updates: any) {
    await this.delay();
    console.log('🎭 MOCK: Updating budget:', budgetId);
    const budgetIndex = this.mockData.budgets.findIndex(b => b.id === budgetId);
    if (budgetIndex !== -1) {
      this.mockData.budgets[budgetIndex] = {
        ...this.mockData.budgets[budgetIndex],
        ...updates,
        updated_at: new Date().toISOString()
      };
      return this.mockData.budgets[budgetIndex];
    }
    throw new Error('Budget not found');
  }

  async updateGoal(goalId: string, updates: any) {
    await this.delay();
    console.log('🎭 MOCK: Updating goal:', goalId);
    const goalIndex = this.mockData.goals.findIndex(g => g.id === goalId);
    if (goalIndex !== -1) {
      this.mockData.goals[goalIndex] = {
        ...this.mockData.goals[goalIndex],
        ...updates,
        updated_at: new Date().toISOString()
      };
      return this.mockData.goals[goalIndex];
    }
    throw new Error('Goal not found');
  }

  // Delete methods (simulate success)
  async deleteAccount(accountId: string) {
    await this.delay();
    console.log('🎭 MOCK: Deleting account:', accountId);
    this.mockData.accounts = this.mockData.accounts.filter(a => a.id !== accountId);
  }

  async deleteTransaction(transactionId: string) {
    await this.delay();
    console.log('🎭 MOCK: Deleting transaction:', transactionId);
    this.mockData.transactions = this.mockData.transactions.filter(t => t.id !== transactionId);
  }

  async deleteBudget(budgetId: string) {
    await this.delay();
    console.log('🎭 MOCK: Deleting budget:', budgetId);
    this.mockData.budgets = this.mockData.budgets.filter(b => b.id !== budgetId);
  }

  async deleteGoal(goalId: string) {
    await this.delay();
    console.log('🎭 MOCK: Deleting goal:', goalId);
    this.mockData.goals = this.mockData.goals.filter(g => g.id !== goalId);
  }

  async updateGoalProgress(goalId: string, progressData: { current_amount: number }) {
    await this.delay();
    console.log('🎭 MOCK: Updating goal progress:', goalId, progressData);
    const goal = this.mockData.goals.find(g => g.id === goalId);
    if (goal) {
      goal.current_amount = progressData.current_amount;
      goal.progress = Math.min((progressData.current_amount / goal.target_amount) * 100, 100);
    }
    return goal;
  }
}

export const mockPersonalFinanceService = new MockPersonalFinanceService();
