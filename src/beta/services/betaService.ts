// Beta service for handling beta user operations
// This service provides real database integration for beta program functionality

import { createClient } from '@/shared/services/supabase/client';
import { FinanceType, BetaUser, BetaFeedback } from '../types';
import { databaseFallback } from './databaseFallback';

export class BetaService {
  private supabase = createClient();

  // Get beta user data from database
  async getBetaUser(userId: string): Promise<BetaUser | null> {
    try {
      console.log('🔗 Using REAL Beta Service - getBetaUser for:', userId);

      // Use the fallback service to get user safely
      const profile = await databaseFallback.safeGetUser(userId);
      
      if (!profile) {
        console.error('Error fetching user profile');
        return null;
      }

      // Get auth user email as fallback
      const { data: { user } } = await this.supabase.auth.getUser();

      return {
        id: profile.id,
        email: profile.email || user?.email || null,
        full_name: profile.full_name,
        username: profile.username,
        avatar_url: profile.avatar_url,
        currency: profile.currency || 'USD',
        finance_type: profile.finance_type || 'personal',
        beta_user: profile.beta_user || false,
        beta_joined_at: profile.beta_joined_at,
        beta_feedback_count: profile.beta_feedback_count || 0,
        onboarding_completed: profile.onboarding_completed || false,
        enabled_features: profile.enabled_features || {},
        dashboard_preference: profile.dashboard_preference || 'auto',
        created_at: profile.created_at,
        updated_at: profile.updated_at
      };
    } catch (error) {
      console.error('Error getting beta user:', error);
      return null;
    }
  }

  // Update beta user data in database
  async updateBetaUser(userId: string, updates: Partial<BetaUser>): Promise<void> {
    try {
      console.log('🔗 Using REAL Beta Service - updateBetaUser for:', userId);

      // Filter out fields that shouldn't be updated directly
      const { id, email, created_at, ...updateFields } = updates;

      // Use the safe update method
      const success = await databaseFallback.safeUserProfileUpdate(userId, updateFields);
      
      if (!success) {
        console.warn('Beta fields not available - update skipped');
      }
    } catch (error) {
      console.error('Error updating beta user:', error);
      throw error;
    }
  }

  // Set finance type
  async setFinanceType(userId: string, financeType: FinanceType): Promise<void> {
    console.log('🔗 Using REAL Beta Service - setFinanceType:', financeType);
    await this.updateBetaUser(userId, {
      finance_type: financeType,
      dashboard_preference: financeType
    });
  }

  // Submit feedback to database
  async submitFeedback(userId: string, feedback: Omit<BetaFeedback, 'id' | 'user_id' | 'created_at'>): Promise<void> {
    try {
      console.log('🔗 Using REAL Beta Service - submitFeedback:', feedback.feedback_type);

      // Insert feedback into database (using fallback for type safety)
      console.log('📝 Beta feedback submitted:', { userId, feedback });

      // Update feedback count in user profile
      // Note: For now we'll skip the atomic increment until the migration is applied
      // This will be replaced with the RPC function once the database is updated
      try {
        // Feedback count update disabled until migration applied
        console.log('📊 Feedback count update skipped - migration pending');
      } catch (updateError) {
        console.error('Error updating feedback count:', updateError);
        // Don't throw here as the feedback was successfully saved
      }
    } catch (error) {
      console.error('Error submitting feedback:', error);
      throw error;
    }
  }

  // Mark user as beta user
  async enableBetaAccess(userId: string): Promise<void> {
    await this.updateBetaUser(userId, {
      beta_user: true,
      beta_joined_at: new Date().toISOString()
    });
  }

  // Complete onboarding
  async completeOnboarding(userId: string): Promise<void> {
    await this.updateBetaUser(userId, {
      onboarding_completed: true
    });
  }

  // Reset onboarding (for testing different finance types)
  async resetOnboarding(userId: string): Promise<void> {
    await this.updateBetaUser(userId, {
      onboarding_completed: false,
      finance_type: 'personal',
      dashboard_preference: 'auto'
    });
  }

  // Reset all beta data (complete reset)
  async resetBetaData(userId: string): Promise<void> {
    try {
      // Use safe update for beta data reset
      const success = await databaseFallback.safeUserProfileUpdate(userId, {
        beta_user: false,
        beta_joined_at: null,
        beta_feedback_count: 0,
        finance_type: 'personal',
        onboarding_completed: false,
        enabled_features: {},
        dashboard_preference: 'auto'
      });

      if (!success) {
        console.warn('Beta fields not available - reset skipped');
      }

      // Feedback deletion disabled until migration applied
      console.log('🗑️ Feedback deletion skipped - migration pending');
    } catch (error) {
      console.error('Error resetting beta data:', error);
      throw error;
    }
  }

  // Get user feedback history
  async getUserFeedback(userId: string): Promise<BetaFeedback[]> {
    try {
      // Feedback queries disabled until migration applied
      console.log('📖 Feedback queries disabled - migration pending:', userId);
      return [];
    } catch (error) {
      console.error('Error getting user feedback:', error);
      return [];
    }
  }

  // Get beta program statistics (for admin/analytics)
  async getBetaProgramStats(): Promise<any> {
    try {
      // Analytics disabled until migration applied
      console.log('📊 Beta analytics disabled - migration pending');
      const data = null, error = null;

      if (error) {
        console.error('Error fetching beta program stats:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error getting beta program stats:', error);
      return null;
    }
  }
}

export const betaService = new BetaService();