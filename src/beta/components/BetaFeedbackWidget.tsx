'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/shared/components/ui/button';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from '@/shared/components/ui/dialog';
import { Textarea } from '@/shared/components/ui/textarea';
import { Label } from '@/shared/components/ui/label';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/shared/components/ui/select';
import { MessageCircle, Star, Send } from 'lucide-react';
import { useBetaFinanceType } from '../contexts/BetaFinanceTypeContext';
import { useToast } from '@/shared/hooks/use-toast';

interface BetaFeedbackWidgetProps {
  trigger?: React.ReactNode;
  className?: string;
}

export function BetaFeedbackWidget({ trigger, className }: BetaFeedbackWidgetProps) {
  const [open, setOpen] = useState(false);
  const [feedbackType, setFeedbackType] = useState<string>('');
  const [content, setContent] = useState('');
  const [rating, setRating] = useState<number | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const { submitFeedback, user } = useBetaFinanceType();
  const { toast } = useToast();

  const handleSubmit = async () => {
    if (!feedbackType || (!content && feedbackType !== 'rating')) {
      toast({
        title: 'Please complete all fields',
        description: 'Feedback type and content are required.',
        variant: 'destructive'
      });
      return;
    }

    setIsSubmitting(true);
    try {
      await submitFeedback({
        type: feedbackType,
        content: content || undefined,
        rating: rating || undefined
      });

      toast({
        title: 'Feedback submitted!',
        description: 'Thank you for helping improve our beta program.',
      });

      // Reset form
      setFeedbackType('');
      setContent('');
      setRating(null);
      setOpen(false);
    } catch {
      toast({
        title: 'Error submitting feedback',
        description: 'Please try again later.',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const defaultTrigger = (
    <Button 
      variant="outline" 
      size="sm" 
      className={className}
    >
      <MessageCircle className="w-4 h-4 mr-2" />
      Beta Feedback
    </Button>
  );

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || defaultTrigger}
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <MessageCircle className="w-5 h-5 mr-2" />
            Beta Program Feedback
          </DialogTitle>
          <DialogDescription>
            Help us improve the beta experience by sharing your thoughts and suggestions.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div>
            <Label htmlFor="feedback-type">Feedback Type</Label>
            <Select value={feedbackType} onValueChange={setFeedbackType}>
              <SelectTrigger>
                <SelectValue placeholder="Select feedback type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="bug">Bug Report</SelectItem>
                <SelectItem value="feature_request">Feature Request</SelectItem>
                <SelectItem value="general">General Feedback</SelectItem>
                <SelectItem value="rating">Overall Rating</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {feedbackType === 'rating' && (
            <div>
              <Label>How would you rate your beta experience?</Label>
              <div className="flex space-x-1 mt-2">
                {[1, 2, 3, 4, 5].map((star) => (
                  <button
                    key={star}
                    onClick={() => setRating(star)}
                    className={`p-1 hover:scale-110 transition-transform ${
                      rating && star <= rating 
                        ? 'text-yellow-500' 
                        : 'text-gray-300 hover:text-yellow-400'
                    }`}
                  >
                    <Star className={`w-6 h-6 ${
                      rating && star <= rating ? 'fill-current' : ''
                    }`} />
                  </button>
                ))}
              </div>
              {rating && (
                <p className="text-sm text-muted-foreground mt-1">
                  {rating === 1 && 'Poor - needs significant improvement'}
                  {rating === 2 && 'Fair - some issues to address'}
                  {rating === 3 && 'Good - generally satisfied'}
                  {rating === 4 && 'Very good - mostly excellent'}
                  {rating === 5 && 'Excellent - exceeds expectations'}
                </p>
              )}
            </div>
          )}

          <div>
            <Label htmlFor="content">
              {feedbackType === 'rating' ? 'Additional Comments (Optional)' : 'Details'}
            </Label>
            <Textarea
              id="content"
              value={content}
              onChange={(e) => setContent(e.target.value)}
              placeholder={
                feedbackType === 'bug' 
                  ? 'Please describe the bug, what you expected to happen, and steps to reproduce...'
                  : feedbackType === 'feature_request'
                  ? 'Describe the feature you\'d like to see and how it would help...'
                  : 'Share your thoughts, suggestions, or experiences...'
              }
              rows={4}
            />
          </div>

          <div className="flex justify-between items-center">
            <div className="text-xs text-muted-foreground">
              Feedback count: {user?.beta_feedback_count || 0}
            </div>
            <div className="flex space-x-2">
              <Button 
                variant="outline" 
                onClick={() => setOpen(false)}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button onClick={handleSubmit} disabled={isSubmitting}>
                <Send className="w-4 h-4 mr-2" />
                {isSubmitting ? 'Submitting...' : 'Submit'}
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}