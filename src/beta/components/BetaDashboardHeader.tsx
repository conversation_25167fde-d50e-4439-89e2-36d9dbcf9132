"use client";

import React from "react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/shared/components/ui/button";
import { Badge } from "@/shared/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/shared/components/ui/dropdown-menu";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@/shared/components/ui/avatar";
import {
  Sparkles,
  Settings,
  LogOut,
  User,
  Menu,
  Bell,
  MessageCircle,
  RotateCcw,
  Sun,
  Moon,
} from "lucide-react";
import { useBetaFinanceType } from "@/beta/contexts/BetaFinanceTypeContext";
import { BetaFeedbackWidget } from "./BetaFeedbackWidget";
import { createClient } from "@/shared/services/supabase/client";
import { useRouter } from "next/navigation";
import { betaService } from "@/beta/services/betaService";
import { useToast } from "@/shared/hooks/use-toast";
import { getDataModeInfo } from "@/beta/services/serviceFactory";
import { DataModeToggle } from "./DataModeToggle";
import { LanguageSwitcher } from "@/shared/components/ui/language-switcher";
import { useTheme } from "next-themes";

export function BetaDashboardHeader() {
  const { user, financeType, refreshUser, dataMode, setDataMode } = useBetaFinanceType();
  const dataModeInfo = getDataModeInfo(dataMode);
  const { theme, setTheme } = useTheme();
  const supabase = createClient();
  const router = useRouter();
  const { toast } = useToast();

  const handleSignOut = async () => {
    await supabase.auth.signOut();
    router.push("/beta/auth/login");
  };

  const handleResetOnboarding = async () => {
    if (!user) return;

    try {
      // Clear all onboarding-related localStorage
      const onboardingKeys = [
        "beta-onboarding-data",
        "beta-onboarding-step",
        "beta-onboarding-completed",
        "beta-onboarding-final-data",
        "beta-onboarding-completed-at",
      ];

      onboardingKeys.forEach((key) => {
        if (typeof window !== "undefined") {
          localStorage.removeItem(key);
        }
      });

      // Reset onboarding in the service
      await betaService.resetOnboarding(user.id);

      // Refresh user context to get updated state
      await refreshUser();

      toast({
        title: "Onboarding Reset",
        description: "You can now choose a different finance type!",
      });

      router.push("/beta/onboarding");
    } catch (error) {
      console.error("Reset onboarding error:", error);
      toast({
        title: "Reset failed",
        description: "Please try again later.",
        variant: "destructive",
      });
    }
  };

  const getFinanceTypeLabel = () => {
    switch (financeType) {
      case "personal":
        return "Personal Finance";
      case "family":
        return "Family Finance";
      case "combined":
        return "Combined Finance";
      default:
        return "Beta User";
    }
  };

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-16 items-center justify-between px-4">
        {/* Left: Logo and Beta Badge */}
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="icon" className="lg:hidden">
            <Menu className="h-5 w-5" />
          </Button>

          <Link href="/beta/dashboard" className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">BT</span>
            </div>
            <span className="text-xl font-bold">Budget Tracker</span>
          </Link>

          <div className="flex items-center space-x-2">
            <Badge
              variant="secondary"
              className="bg-gradient-to-r from-blue-600 to-purple-600 text-white"
            >
              <Sparkles className="w-3 h-3 mr-1" />
              BETA
            </Badge>
            <Badge variant="outline" className="text-xs">
              {getFinanceTypeLabel()}
            </Badge>
            <Badge
              variant="outline"
              className={`text-xs ${dataMode === 'mock' ? 'border-amber-300 text-amber-700 bg-amber-50' : 'border-green-300 text-green-700 bg-green-50'}`}
            >
              <span className="mr-1">{dataModeInfo.icon}</span>
              {dataMode === 'mock' ? 'Mock Data' : 'Live Data'}
            </Badge>
          </div>
        </div>

        {/* Right: Actions and User Menu */}
        <div className="flex items-center space-x-4">
          {/* Language Switcher */}
          <LanguageSwitcher variant="full" />

          {/* Theme Toggle */}
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
            aria-label="Toggle theme"
          >
            <Sun className="h-5 w-5 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
            <Moon className="absolute h-5 w-5 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
            <span className="sr-only">Toggle theme</span>
          </Button>

          {/* Beta Feedback Widget */}
          <BetaFeedbackWidget
            trigger={
              <Button variant="outline" size="sm">
                <MessageCircle className="w-4 h-4 mr-2" />
                Feedback
              </Button>
            }
          />

          {/* Notifications */}
          <Button variant="ghost" size="icon" disabled>
            <Bell className="h-5 w-5" />
          </Button>

          {/* User Menu */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="relative h-10 w-10 rounded-full"
              >
                <Avatar className="h-10 w-10">
                  <AvatarImage
                    src={user?.avatar_url || undefined}
                    alt={user?.full_name || "User"}
                  />
                  <AvatarFallback>
                    {user?.full_name?.charAt(0)?.toUpperCase() || "U"}
                  </AvatarFallback>
                </Avatar>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56" align="end" forceMount>
              <DropdownMenuLabel className="font-normal">
                <div className="flex flex-col space-y-1">
                  <p className="text-sm font-medium leading-none">
                    {user?.full_name || "Beta User"}
                  </p>
                  <p className="text-xs leading-none text-muted-foreground">
                    {user?.email}
                  </p>
                  <div className="flex items-center space-x-1 mt-1">
                    <Badge variant="secondary" className="text-xs">
                      Beta Member
                    </Badge>
                    <span className="text-xs text-muted-foreground">
                      • {user?.beta_feedback_count || 0} feedback
                    </span>
                  </div>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem asChild>
                <Link href="/beta/dashboard/settings">
                  <User className="mr-2 h-4 w-4" />
                  <span>Profile</span>
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href="/beta/dashboard/settings">
                  <Settings className="mr-2 h-4 w-4" />
                  <span>Settings</span>
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleResetOnboarding}>
                <RotateCcw className="mr-2 h-4 w-4" />
                <span>Reset Onboarding</span>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <div className="px-2 py-1">
                <DataModeToggle showDescription={false} />
              </div>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleSignOut}>
                <LogOut className="mr-2 h-4 w-4" />
                <span>Log out</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  );
}
