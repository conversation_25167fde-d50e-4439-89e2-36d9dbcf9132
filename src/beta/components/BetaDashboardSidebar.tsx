'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { But<PERSON> } from '@/shared/components/ui/button';
import { Badge } from '@/shared/components/ui/badge';
import { Separator } from '@/shared/components/ui/separator';
import { cn } from '@/shared/utils';
import { useBetaFinanceType } from '@/beta/contexts/BetaFinanceTypeContext';
import { getNavigationForType } from '@/beta/config/finance-journeys';
import { 
  LayoutDashboard, 
  CreditCard, 
  ArrowRightLeft, 
  PieChart, 
  Target, 
  BarChart3,
  Users,
  User,
  Layers,
  TrendingUp,
  Settings,
  HelpCircle,
  Sparkles
} from 'lucide-react';

const iconMap = {
  LayoutDashboard,
  CreditCard,
  ArrowRightLeft,
  PieChart,
  Target,
  BarChart3,
  Users,
  User,
  Layers,
  TrendingUp,
  Settings,
  HelpCircle
};

export function BetaDashboardSidebar() {
  const pathname = usePathname();
  const { financeType } = useBetaFinanceType();
  
  const navigation = getNavigationForType(financeType);

  const betaFeatures = [
    { 
      href: '/beta/dashboard/ai-insights', 
      label: 'AI Insights', 
      icon: 'TrendingUp',
      badge: 'New'
    },
    { 
      href: '/beta/dashboard/smart-budgets', 
      label: 'Smart Budgets', 
      icon: 'PieChart',
      badge: 'Beta'
    }
  ];

  return (
    <div className="fixed left-0 top-16 z-40 h-[calc(100vh-4rem)] w-64 border-r bg-background lg:block hidden">
      <div className="flex h-full flex-col">
        <div className="flex-1 overflow-auto py-4">
          {/* Main Navigation */}
          <div className="px-3 py-2">
            <h2 className="mb-2 px-4 text-sm font-semibold tracking-tight text-muted-foreground">
              Navigation
            </h2>
            <div className="space-y-1">
              {navigation.map((item) => {
                const Icon = iconMap[item.icon as keyof typeof iconMap];
                return (
                  <Button
                    key={item.href}
                    asChild
                    variant={pathname === item.href ? 'secondary' : 'ghost'}
                    className={cn(
                      'w-full justify-start',
                      pathname === item.href && 'bg-secondary'
                    )}
                  >
                    <Link href={item.href}>
                      <Icon className="mr-2 h-4 w-4" />
                      {item.label}
                    </Link>
                  </Button>
                );
              })}
            </div>
          </div>

          <Separator className="my-4" />

          {/* Beta Features */}
          <div className="px-3 py-2">
            <div className="flex items-center justify-between mb-2 px-4">
              <h2 className="text-sm font-semibold tracking-tight text-muted-foreground">
                Beta Features
              </h2>
              <Sparkles className="h-3 w-3 text-purple-600" />
            </div>
            <div className="space-y-1">
              {betaFeatures.map((item) => {
                const Icon = iconMap[item.icon as keyof typeof iconMap];
                return (
                  <Button
                    key={item.href}
                    asChild
                    variant={pathname === item.href ? 'secondary' : 'ghost'}
                    className={cn(
                      'w-full justify-start',
                      pathname === item.href && 'bg-secondary'
                    )}
                  >
                    <Link href={item.href}>
                      <Icon className="mr-2 h-4 w-4" />
                      <span className="flex-1 text-left">{item.label}</span>
                      <Badge 
                        variant="secondary" 
                        className={cn(
                          'text-xs',
                          item.badge === 'New' && 'bg-green-100 text-green-700',
                          item.badge === 'Beta' && 'bg-blue-100 text-blue-700'
                        )}
                      >
                        {item.badge}
                      </Badge>
                    </Link>
                  </Button>
                );
              })}
            </div>
          </div>

          <Separator className="my-4" />

          {/* Settings & Support */}
          <div className="px-3 py-2">
            <h2 className="mb-2 px-4 text-sm font-semibold tracking-tight text-muted-foreground">
              Support
            </h2>
            <div className="space-y-1">
              <Button
                asChild
                variant={pathname === '/beta/dashboard/settings' ? 'secondary' : 'ghost'}
                className="w-full justify-start"
              >
                <Link href="/beta/dashboard/settings">
                  <Settings className="mr-2 h-4 w-4" />
                  Settings
                </Link>
              </Button>
              <Button
                asChild
                variant={pathname === '/beta/dashboard/help' ? 'secondary' : 'ghost'}
                className="w-full justify-start"
              >
                <Link href="/beta/dashboard/help">
                  <HelpCircle className="mr-2 h-4 w-4" />
                  Help & Feedback
                </Link>
              </Button>
            </div>
          </div>
        </div>

        {/* Beta Program Info */}
        <div className="p-4 border-t">
          <div className="rounded-lg bg-gradient-to-r from-blue-50 to-purple-50 p-3 text-center">
            <div className="flex items-center justify-center space-x-1 mb-1">
              <Sparkles className="h-4 w-4 text-purple-600" />
              <span className="text-sm font-semibold">Beta Program</span>
            </div>
            <p className="text-xs text-muted-foreground mb-2">
              You&apos;re helping shape the future of financial management
            </p>
            <Button asChild size="sm" variant="outline" className="w-full">
              <Link href="/beta/dashboard/beta-status">
                View Beta Status
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}