'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/shared/components/ui/card';
import { Button } from '@/shared/components/ui/button';
import { Badge } from '@/shared/components/ui/badge';
import { Progress } from '@/shared/components/ui/progress';
import { Input } from '@/shared/components/ui/input';
import { Label } from '@/shared/components/ui/label';
import { Textarea } from '@/shared/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/shared/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/shared/components/ui/dialog';
import { 
  Plus, 
  Target, 
  PiggyBank,
  TrendingUp,
  Calendar,
  DollarSign,
  CheckCircle,
  Clock,
  Pause,
  Edit,
  Trash2,
  Trophy
} from 'lucide-react';
import { PersonalGoal, PersonalGoalFormData } from '../types/personal';
import { getPersonalFinanceService } from '@/beta/services/serviceFactory';
import { useBetaFinanceType } from '@/beta/contexts/BetaFinanceTypeContext';
import { useToast } from '@/shared/hooks/use-toast';

export function PersonalGoals() {
  const [goals, setGoals] = useState<PersonalGoal[]>([]);
  const [loading, setLoading] = useState(true);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isUpdateDialogOpen, setIsUpdateDialogOpen] = useState(false);
  const [selectedGoal, setSelectedGoal] = useState<PersonalGoal | null>(null);
  const [updateAmount, setUpdateAmount] = useState<number>(0);
  const [formData, setFormData] = useState<PersonalGoalFormData>({
    name: '',
    description: '',
    target_amount: 0,
    target_date: new Date(new Date().setFullYear(new Date().getFullYear() + 1)).toISOString().split('T')[0],
    category: 'saving'
  });
  const { user, dataMode } = useBetaFinanceType();
  const { toast } = useToast();

  useEffect(() => {
    if (user) {
      loadGoals();
    }
  }, [user, dataMode]);

  const loadGoals = async () => {
    if (!user) return;

    try {
      setLoading(true);
      const personalService = getPersonalFinanceService(dataMode);
      const goalData = await personalService.getGoals(user.id);
      setGoals(goalData);
    } catch (error) {
      console.error('Error loading goals:', error);
      toast({
        title: 'Error',
        description: 'Failed to load goals. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCreateGoal = async () => {
    if (!user || !formData.name || formData.target_amount <= 0) {
      toast({
        title: 'Invalid data',
        description: 'Please fill in all required fields.',
        variant: 'destructive'
      });
      return;
    }

    try {
      const personalService = getPersonalFinanceService(dataMode);
      const newGoal = await personalService.createGoal(user.id, formData);
      setGoals([newGoal, ...goals]);
      setIsAddDialogOpen(false);
      setFormData({
        name: '',
        description: '',
        target_amount: 0,
        target_date: new Date(new Date().setFullYear(new Date().getFullYear() + 1)).toISOString().split('T')[0],
        category: 'saving'
      });
      
      toast({
        title: 'Goal Created',
        description: `Goal "${newGoal.name}" has been created successfully.`,
      });
    } catch (error) {
      console.error('Error creating goal:', error);
      toast({
        title: 'Error',
        description: 'Failed to create goal. Please try again.',
        variant: 'destructive'
      });
    }
  };

  const handleUpdateProgress = async () => {
    if (!selectedGoal) return;

    try {
      const personalService = getPersonalFinanceService(dataMode);
      const updatedGoal = await (personalService as any).updateGoalProgress(selectedGoal.id, updateAmount);
      if (updatedGoal) {
        setGoals(goals.map(goal => goal.id === updatedGoal.id ? updatedGoal : goal));
      }
      setIsUpdateDialogOpen(false);
      setSelectedGoal(null);
      setUpdateAmount(0);
      
      toast({
        title: 'Progress Updated',
        description: `Goal progress has been updated successfully.`,
      });
    } catch (error) {
      console.error('Error updating goal progress:', error);
      toast({
        title: 'Error',
        description: 'Failed to update goal progress. Please try again.',
        variant: 'destructive'
      });
    }
  };

  const openUpdateDialog = (goal: PersonalGoal) => {
    setSelectedGoal(goal);
    setUpdateAmount(goal.current_amount);
    setIsUpdateDialogOpen(true);
  };

  const getGoalStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-blue-100 text-blue-700 border-blue-200';
      case 'completed': return 'bg-green-100 text-green-700 border-green-200';
      case 'paused': return 'bg-gray-100 text-gray-700 border-gray-200';
      default: return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  const getGoalStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <Target className="h-4 w-4" />;
      case 'completed': return <CheckCircle className="h-4 w-4" />;
      case 'paused': return <Pause className="h-4 w-4" />;
      default: return <Target className="h-4 w-4" />;
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'saving': return <PiggyBank className="h-4 w-4" />;
      case 'debt_payoff': return <TrendingUp className="h-4 w-4" />;
      case 'investment': return <DollarSign className="h-4 w-4" />;
      case 'emergency_fund': return <Target className="h-4 w-4" />;
      default: return <Target className="h-4 w-4" />;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'saving': return 'bg-green-100 text-green-700';
      case 'debt_payoff': return 'bg-red-100 text-red-700';
      case 'investment': return 'bg-blue-100 text-blue-700';
      case 'emergency_fund': return 'bg-orange-100 text-orange-700';
      default: return 'bg-gray-100 text-gray-700';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: user?.currency || 'USD'
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getDaysRemaining = (targetDate: string) => {
    const today = new Date();
    const target = new Date(targetDate);
    const diffTime = target.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const getMonthlyTarget = (goal: PersonalGoal) => {
    const daysRemaining = getDaysRemaining(goal.target_date);
    const monthsRemaining = Math.max(1, daysRemaining / 30);
    const remainingAmount = goal.target_amount - goal.current_amount;
    return remainingAmount / monthsRemaining;
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <div className="h-8 bg-gray-200 rounded w-48 mb-2"></div>
            <div className="h-4 bg-gray-200 rounded w-64"></div>
          </div>
          <div className="h-10 bg-gray-200 rounded w-32"></div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(3)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-6 bg-gray-200 rounded w-3/4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="h-4 bg-gray-200 rounded"></div>
                  <div className="h-2 bg-gray-200 rounded"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  const activeGoals = goals.filter(goal => goal.status === 'active');
  const completedGoals = goals.filter(goal => goal.status === 'completed');

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Personal Goals</h2>
          <p className="text-muted-foreground">
            Track your progress towards financial milestones
          </p>
        </div>
        
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              Add Goal
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Create New Goal</DialogTitle>
              <DialogDescription>
                Set up a new financial goal to track your progress.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="name">Goal Name</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  placeholder="e.g., Emergency Fund, Vacation"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="description">Description (Optional)</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  placeholder="Brief description of your goal..."
                  rows={2}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="category">Category</Label>
                <Select value={formData.category} onValueChange={(value: 'saving' | 'debt_payoff' | 'investment' | 'emergency_fund') => setFormData({ ...formData, category: value })}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="saving">💰 Saving</SelectItem>
                    <SelectItem value="debt_payoff">💳 Debt Payoff</SelectItem>
                    <SelectItem value="investment">📈 Investment</SelectItem>
                    <SelectItem value="emergency_fund">🚨 Emergency Fund</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="target_amount">Target Amount</Label>
                <Input
                  id="target_amount"
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.target_amount}
                  onChange={(e) => setFormData({ ...formData, target_amount: parseFloat(e.target.value) || 0 })}
                  placeholder="0.00"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="target_date">Target Date</Label>
                <Input
                  id="target_date"
                  type="date"
                  value={formData.target_date}
                  onChange={(e) => setFormData({ ...formData, target_date: e.target.value })}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleCreateGoal}>Create Goal</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {/* Goal Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Goals</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{activeGoals.length}</div>
            <p className="text-xs text-muted-foreground">
              In progress
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed</CardTitle>
            <Trophy className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{completedGoals.length}</div>
            <p className="text-xs text-muted-foreground">
              Successfully achieved
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Target</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(activeGoals.reduce((sum, goal) => sum + goal.target_amount, 0))}
            </div>
            <p className="text-xs text-muted-foreground">
              Active goals target
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Progress</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(activeGoals.reduce((sum, goal) => sum + goal.current_amount, 0))}
            </div>
            <p className="text-xs text-muted-foreground">
              Current progress
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Goals List */}
      {goals.length === 0 ? (
        <Card>
          <CardContent className="text-center py-8">
            <Target className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No goals yet</h3>
            <p className="text-muted-foreground mb-4">
              Create your first financial goal to start tracking your progress towards important milestones.
            </p>
            <Button onClick={() => setIsAddDialogOpen(true)}>
              <Plus className="w-4 h-4 mr-2" />
              Create Your First Goal
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-6">
          {/* Active Goals */}
          {activeGoals.length > 0 && (
            <div>
              <h3 className="text-lg font-semibold mb-4">Active Goals</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {activeGoals.map((goal) => (
                  <Card key={goal.id} className="relative">
                    <CardHeader>
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <CardTitle className="text-lg">{goal.name}</CardTitle>
                          {goal.description && (
                            <CardDescription className="mt-1">
                              {goal.description}
                            </CardDescription>
                          )}
                        </div>
                        <div className="flex flex-col gap-2">
                          <Badge 
                            variant="outline" 
                            className={`${getGoalStatusColor(goal.status)} flex items-center gap-1`}
                          >
                            {getGoalStatusIcon(goal.status)}
                            {goal.status}
                          </Badge>
                          {goal.category && (
                            <Badge 
                              variant="outline" 
                              className={`${getCategoryColor(goal.category)} flex items-center gap-1`}
                            >
                              {getCategoryIcon(goal.category)}
                              {goal.category.replace('_', ' ')}
                            </Badge>
                          )}
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span>Progress</span>
                            <span className="font-medium">
                              {formatCurrency(goal.current_amount)} / {formatCurrency(goal.target_amount)}
                            </span>
                          </div>
                          <Progress 
                            value={Math.min(goal.progress_percentage, 100)} 
                            className="h-3"
                          />
                          <div className="flex justify-between text-xs text-muted-foreground">
                            <span>{goal.progress_percentage.toFixed(1)}% complete</span>
                            <span>{formatCurrency(goal.target_amount - goal.current_amount)} remaining</span>
                          </div>
                        </div>

                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <p className="text-muted-foreground">Target Date</p>
                            <p className="font-medium flex items-center gap-1">
                              <Calendar className="h-3 w-3" />
                              {formatDate(goal.target_date)}
                            </p>
                          </div>
                          <div>
                            <p className="text-muted-foreground">Days Left</p>
                            <p className="font-medium flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              {getDaysRemaining(goal.target_date)} days
                            </p>
                          </div>
                        </div>

                        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                          <p className="text-sm text-blue-700">
                            <strong>Monthly Target:</strong> {formatCurrency(getMonthlyTarget(goal))}
                          </p>
                          <p className="text-xs text-blue-600 mt-1">
                            Save this amount monthly to reach your goal on time
                          </p>
                        </div>

                        <div className="flex gap-2">
                          <Button 
                            variant="outline" 
                            size="sm" 
                            className="flex-1"
                            onClick={() => openUpdateDialog(goal)}
                          >
                            <Edit className="w-3 h-3 mr-1" />
                            Update
                          </Button>
                          <Button variant="outline" size="sm" className="flex-1">
                            <Trash2 className="w-3 h-3 mr-1" />
                            Delete
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          )}

          {/* Completed Goals */}
          {completedGoals.length > 0 && (
            <div>
              <h3 className="text-lg font-semibold mb-4">Completed Goals</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {completedGoals.map((goal) => (
                  <Card key={goal.id} className="relative border-green-200 bg-green-50/50">
                    <CardHeader>
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <CardTitle className="text-lg flex items-center gap-2">
                            <Trophy className="h-5 w-5 text-green-600" />
                            {goal.name}
                          </CardTitle>
                          {goal.description && (
                            <CardDescription className="mt-1">
                              {goal.description}
                            </CardDescription>
                          )}
                        </div>
                        <Badge className="bg-green-100 text-green-700 border-green-200">
                          <CheckCircle className="h-3 w-3 mr-1" />
                          Completed
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>Achieved</span>
                          <span className="font-medium text-green-600">
                            {formatCurrency(goal.target_amount)}
                          </span>
                        </div>
                        <Progress value={100} className="h-2" />
                        <p className="text-xs text-green-600 text-center">
                          🎉 Goal completed successfully!
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Update Progress Dialog */}
      <Dialog open={isUpdateDialogOpen} onOpenChange={setIsUpdateDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Update Goal Progress</DialogTitle>
            <DialogDescription>
              Update the current amount for "{selectedGoal?.name}"
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="current_amount">Current Amount</Label>
              <Input
                id="current_amount"
                type="number"
                min="0"
                step="0.01"
                value={updateAmount}
                onChange={(e) => setUpdateAmount(parseFloat(e.target.value) || 0)}
                placeholder="0.00"
              />
              {selectedGoal && (
                <p className="text-xs text-muted-foreground">
                  Target: {formatCurrency(selectedGoal.target_amount)}
                </p>
              )}
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsUpdateDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleUpdateProgress}>Update Progress</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}