// Family Finance Types
// These types define the data structures for family financial management

export interface FamilyFinanceData {
  groups: FamilyGroup[];
  accounts: FamilyAccount[];
  transactions: FamilyTransaction[];
  budgets: FamilyBudget[];
  goals: FamilyGoal[];
  analytics: FamilyAnalytics;
}

export interface FamilyGroup {
  id: string;
  name: string;
  description?: string;
  owner_user_id: string;
  member_count: number;
  balance: number;
  monthly_income: number;
  monthly_expenses: number;
  currency: string;
  is_admin: boolean;
  is_member: boolean;
  created_at: string;
  updated_at: string;
}

export interface FamilyGroupMember {
  id: string;
  group_id: string;
  user_id: string;
  role: 'admin' | 'member' | 'viewer';
  permissions: FamilyGroupPermissions;
  joined_at: string;
  user_profile?: {
    full_name?: string;
    username?: string;
    avatar_url?: string;
    email?: string;
  };
}

export interface FamilyGroupPermissions {
  can_view_transactions: boolean;
  can_add_transactions: boolean;
  can_edit_transactions: boolean;
  can_delete_transactions: boolean;
  can_manage_budgets: boolean;
  can_manage_goals: boolean;
  can_manage_accounts: boolean;
  can_invite_members: boolean;
  can_remove_members: boolean;
  can_edit_group: boolean;
}

export interface FamilyAccount {
  id: string;
  family_group_id: string;
  name: string;
  type: string;
  balance: number;
  currency: string;
  institution?: string;
  last_synced?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  created_by_user_id: string;
  permissions: {
    can_view: string[];
    can_edit: string[];
    can_transact: string[];
  };
}

export interface FamilyTransaction {
  id: string;
  family_group_id: string;
  account_id?: string;
  category_id?: string;
  amount: number;
  description: string;
  type: 'income' | 'expense' | 'transfer';
  date: string;
  currency: string;
  created_by: string;
  created_at: string;
  updated_at: string;
  category?: {
    id: string;
    name: string;
    icon?: string | null;
    color: string;
    type: string;
  };
  account?: {
    id: string;
    name: string;
    type: string;
  };
  created_by_user?: {
    full_name?: string;
    username?: string;
  };
}

export interface FamilyBudget {
  id: string;
  family_group_id: string;
  name: string;
  category_id?: string;
  amount: number;
  period: 'weekly' | 'monthly' | 'yearly';
  period_start: string;
  period_end: string;
  spent: number;
  remaining: number;
  percentage_used: number;
  status: 'on_track' | 'warning' | 'exceeded';
  created_by: string;
  created_at: string;
  updated_at: string;
  category?: {
    id: string;
    name: string;
    icon?: string | null;
    color: string;
    type: string;
  };
  contributors: FamilyBudgetContributor[];
}

export interface FamilyBudgetContributor {
  user_id: string;
  amount_contributed: number;
  percentage_contribution: number;
  user_profile?: {
    full_name?: string;
    username?: string;
  };
}

export interface FamilyGoal {
  id: string;
  family_group_id: string;
  name: string;
  description?: string;
  target_amount: number;
  current_amount: number;
  target_date?: string;
  status: 'active' | 'completed' | 'paused' | 'cancelled';
  progress_percentage: number;
  monthly_target: number;
  days_remaining?: number;
  currency: string;
  created_by: string;
  created_at: string;
  updated_at: string;
  contributors: FamilyGoalContributor[];
}

export interface FamilyGoalContributor {
  user_id: string;
  amount_contributed: number;
  percentage_contribution: number;
  last_contribution_date?: string;
  user_profile?: {
    full_name?: string;
    username?: string;
  };
}

export interface FamilyAnalytics {
  overview: FamilyOverviewAnalytics;
  spending_by_category: FamilySpendingByCategory[];
  income_vs_expenses: FamilyIncomeVsExpenses[];
  budget_performance: FamilyBudgetPerformance[];
  goal_progress: FamilyGoalProgress[];
  member_contributions: FamilyMemberContribution[];
  monthly_trends: FamilyMonthlyTrend[];
}

export interface FamilyOverviewAnalytics {
  total_balance: number;
  total_income: number;
  total_expenses: number;
  net_income: number;
  savings_rate: number;
  group_count: number;
  account_count: number;
  active_budgets: number;
  active_goals: number;
  member_count: number;
}

export interface FamilySpendingByCategory {
  category: string;
  amount: number;
  percentage: number;
  color: string;
  transaction_count: number;
  top_contributors: {
    user_id: string;
    amount: number;
    full_name?: string;
  }[];
}

export interface FamilyIncomeVsExpenses {
  month: string;
  income: number;
  expenses: number;
  net: number;
  savings_rate: number;
}

export interface FamilyBudgetPerformance {
  budget_id: string;
  budget_name: string;
  allocated: number;
  spent: number;
  remaining: number;
  percentage_used: number;
  status: 'on_track' | 'warning' | 'exceeded';
  category: string;
  days_remaining: number;
}

export interface FamilyGoalProgress {
  goal_id: string;
  goal_name: string;
  target_amount: number;
  current_amount: number;
  progress_percentage: number;
  monthly_target: number;
  days_remaining?: number;
  on_track: boolean;
}

export interface FamilyMemberContribution {
  user_id: string;
  full_name?: string;
  username?: string;
  total_income: number;
  total_expenses: number;
  net_contribution: number;
  transaction_count: number;
  budget_contributions: number;
  goal_contributions: number;
}

export interface FamilyMonthlyTrend {
  month: string;
  income: number;
  expenses: number;
  net: number;
  member_activity: {
    user_id: string;
    transaction_count: number;
    amount: number;
  }[];
}

// Form Data Types
export interface FamilyGroupFormData {
  name: string;
  description?: string;
  currency?: string;
}

export interface FamilyAccountFormData {
  name: string;
  type: string;
  balance: number;
  currency?: string;
  institution?: string;
}

export interface FamilyTransactionFormData {
  account_id?: string;
  category_id?: string;
  amount: number;
  description: string;
  type: 'income' | 'expense' | 'transfer';
  date: string;
  currency?: string;
}

export interface FamilyBudgetFormData {
  name: string;
  category_id?: string;
  amount: number;
  period: 'weekly' | 'monthly' | 'yearly';
  start_date: string;
}

export interface FamilyGoalFormData {
  name: string;
  description?: string;
  target_amount: number;
  target_date?: string;
  currency?: string;
}

export interface FamilyGroupInvitation {
  id: string;
  group_id: string;
  email: string;
  role: 'admin' | 'member' | 'viewer';
  status: 'pending' | 'accepted' | 'declined' | 'expired';
  invited_by: string;
  created_at: string;
  expires_at: string;
  group?: {
    name: string;
    description?: string;
  };
  invited_by_user?: {
    full_name?: string;
    username?: string;
  };
}
