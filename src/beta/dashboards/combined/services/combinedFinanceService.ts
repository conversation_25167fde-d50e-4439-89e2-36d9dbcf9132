import { createClient } from "@/shared/services/supabase/client";
import {
  CombinedFinanceData,
  FamilyFinanceData,
  FamilyGroup,
  FamilyTransaction,
  UnifiedAnalytics,
  CombinedFinanceInsight,
  UnifiedGoalProgress,
  SpendingComparison,
  CashFlowTrend,
  UserPreferences,
  FinanceMode,
} from "../types/combined";
import { PersonalFinanceService } from "../../personal/services/personalFinanceService";
import { FamilyFinanceService } from "../../family/services/familyFinanceService";

export class CombinedFinanceService {
  private supabase = createClient();
  private personalService = new PersonalFinanceService();
  private familyService = new FamilyFinanceService();

  // Get family finance data specifically
  async getFamilyFinanceData(userId: string): Promise<FamilyFinanceData> {
    const familyData = await this.familyService.getFamilyFinanceData(userId);
    return this.convertFamilyDataToCombinedFormat(familyData);
  }

  // Main method to get all combined finance data
  async getCombinedFinanceData(userId: string): Promise<CombinedFinanceData> {
    try {
      console.log('🔗 Using REAL Combined Finance Service - getCombinedFinanceData for:', userId);

      const [personal, familyData, userPreferences] = await Promise.all([
        this.personalService.getPersonalFinanceData(userId),
        this.familyService.getFamilyFinanceData(userId),
        this.getUserPreferences(userId),
      ]);

      // Convert family data to combined format
      const family = this.convertFamilyDataToCombinedFormat(familyData);

      const unified = await this.getUnifiedAnalytics(userId, personal, family);

      return {
        personal,
        family,
        unified,
        user_preferences: userPreferences,
      };
    } catch (error) {
      console.error("Error fetching combined finance data:", error);
      throw error;
    }
  }

  // Convert family data from FamilyFinanceService to CombinedFinanceService format
  private convertFamilyDataToCombinedFormat(familyData: any): FamilyFinanceData {
    const totalBalance = familyData.accounts?.reduce(
      (sum: number, account: any) => sum + (account.balance || 0),
      0
    ) || 0;

    const monthlyIncome = familyData.analytics?.overview?.total_income || 0;
    const monthlyExpenses = familyData.analytics?.overview?.total_expenses || 0;
    const activeBudgets = familyData.budgets?.filter((b: any) => b.status !== "exceeded").length || 0;
    const activeGoals = familyData.goals?.filter((g: any) => g.status === "active").length || 0;

    // Convert groups to combined format
    const groups = (familyData.groups || []).map((group: any) => ({
      id: group.id,
      name: group.name,
      description: group.description,
      member_count: group.member_count,
      balance: group.balance,
      monthly_income: group.monthly_income,
      monthly_expenses: group.monthly_expenses,
      currency: group.currency,
      is_admin: group.is_admin,
      created_at: group.created_at,
      updated_at: group.updated_at,
    }));

    // Convert transactions to combined format
    const recent_transactions = (familyData.transactions || []).slice(0, 20).map((transaction: any) => ({
      id: transaction.id,
      group_id: transaction.family_group_id,
      group_name: "Family Group", // TODO: Get actual group name
      amount: transaction.amount,
      description: transaction.description,
      type: transaction.type,
      category: transaction.category?.name,
      date: transaction.date,
      created_by: transaction.created_by,
      currency: transaction.currency,
    }));

    return {
      groups,
      total_balance: totalBalance,
      monthly_income: monthlyIncome,
      monthly_expenses: monthlyExpenses,
      active_budgets: activeBudgets,
      active_goals: activeGoals,
      recent_transactions,
    };
  }

  // Get family groups with financial summaries
  private async getFamilyGroups(userId: string): Promise<FamilyGroup[]> {
    const { data: groupMembers, error: membersError } = await this.supabase
      .from("family_group_members")
      .select(
        `
        group_id,
        role,
        family_groups!inner(
          id,
          name,
          description,
          currency,
          created_at,
          updated_at
        )
      `
      )
      .eq("user_id", userId);

    if (membersError) {
      console.error("Error fetching family groups:", membersError);
      return [];
    }

    if (!groupMembers || groupMembers.length === 0) {
      return [];
    }

    // Get financial data for each group
    const groups = await Promise.all(
      groupMembers.map(async (member: any) => {
        const group = member.family_groups;
        const groupId = group.id;

        // Get member count
        const { data: memberCountData } = await this.supabase
          .from("family_group_members")
          .select("id", { count: "exact" })
          .eq("group_id", groupId);

        // Get financial summary for this group
        const [balance, monthlyIncome, monthlyExpenses] = await Promise.all([
          this.getGroupBalance(groupId),
          this.getGroupMonthlyIncome(groupId),
          this.getGroupMonthlyExpenses(groupId),
        ]);

        return {
          id: groupId,
          name: group.name,
          description: group.description,
          member_count: memberCountData?.length || 0,
          balance,
          monthly_income: monthlyIncome,
          monthly_expenses: monthlyExpenses,
          currency: group.currency || "USD",
          is_admin: member.role === "admin",
          created_at: group.created_at,
          updated_at: group.updated_at,
        };
      })
    );

    return groups;
  }

  // Get group financial metrics
  private async getGroupBalance(groupId: string): Promise<number> {
    const { data } = await this.supabase
      .from("accounts")
      .select("balance")
      .eq("group_id", groupId);

    return (data || []).reduce(
      (sum, account) => sum + (account.balance || 0),
      0
    );
  }

  private async getGroupMonthlyIncome(groupId: string): Promise<number> {
    const startOfMonth = new Date();
    startOfMonth.setDate(1);

    const { data } = await this.supabase
      .from("transactions")
      .select("amount")
      .eq("group_id", groupId)
      .eq("type", "income")
      .gte("date", startOfMonth.toISOString().split("T")[0]);

    return (data || []).reduce(
      (sum, transaction) => sum + (transaction.amount || 0),
      0
    );
  }

  private async getGroupMonthlyExpenses(groupId: string): Promise<number> {
    const startOfMonth = new Date();
    startOfMonth.setDate(1);

    const { data } = await this.supabase
      .from("transactions")
      .select("amount")
      .eq("group_id", groupId)
      .eq("type", "expense")
      .gte("date", startOfMonth.toISOString().split("T")[0]);

    return (data || []).reduce(
      (sum, transaction) => sum + Math.abs(transaction.amount || 0),
      0
    );
  }

  // Get family transactions
  private async getFamilyTransactions(
    userId: string,
    limit: number = 20
  ): Promise<FamilyTransaction[]> {
    const { data: groupMembers } = await this.supabase
      .from("family_group_members")
      .select("group_id")
      .eq("user_id", userId);

    if (!groupMembers || groupMembers.length === 0) {
      return [];
    }

    const groupIds = groupMembers.map((member) => member.group_id);

    const { data: transactions } = await this.supabase
      .from("transactions")
      .select(
        `
        id,
        amount,
        description,
        type,
        date,
        currency,
        created_by,
        group_id,
        categories(name),
        family_groups!inner(name)
      `
      )
      .in("group_id", groupIds)
      .order("date", { ascending: false })
      .limit(limit);

    return (transactions || []).map((t: any) => ({
      id: t.id,
      group_id: t.group_id,
      group_name: t.family_groups?.name || "Unknown Group",
      amount: t.amount,
      description: t.description,
      type: t.type,
      category: t.categories?.name,
      date: t.date,
      created_by: t.created_by,
      currency: t.currency || "USD",
    }));
  }

  private async getFamilyBudgetCount(userId: string): Promise<number> {
    const { data: groupMembers } = await this.supabase
      .from("family_group_members")
      .select("group_id")
      .eq("user_id", userId);

    if (!groupMembers || groupMembers.length === 0) {
      return 0;
    }

    const groupIds = groupMembers.map((member) => member.group_id);

    const { data: budgets } = await this.supabase
      .from("budgets")
      .select("id", { count: "exact" })
      .in("group_id", groupIds);

    return budgets?.length || 0;
  }

  private async getFamilyGoalCount(userId: string): Promise<number> {
    const { data: groupMembers } = await this.supabase
      .from("family_group_members")
      .select("group_id")
      .eq("user_id", userId);

    if (!groupMembers || groupMembers.length === 0) {
      return 0;
    }

    const groupIds = groupMembers.map((member) => member.group_id);

    const { data: goals } = await this.supabase
      .from("financial_goals")
      .select("id", { count: "exact" })
      .in("group_id", groupIds)
      .eq("status", "active");

    return goals?.length || 0;
  }

  // Get unified analytics combining personal and family data
  private async getUnifiedAnalytics(
    userId: string,
    personalData: any,
    familyData: FamilyFinanceData
  ): Promise<UnifiedAnalytics> {
    const personalBalance = personalData.accounts.reduce(
      (sum: number, account: any) => sum + account.balance,
      0
    );
    const personalIncome = personalData.analytics.overview.total_income;
    const personalExpenses = personalData.analytics.overview.total_expenses;

    const totalBalance = personalBalance + familyData.total_balance;
    const totalIncome = personalIncome + familyData.monthly_income;
    const totalExpenses = personalExpenses + familyData.monthly_expenses;
    const netCashFlow = totalIncome - totalExpenses;
    const savingsRate = totalIncome > 0 ? (netCashFlow / totalIncome) * 100 : 0;

    const [goalProgress, spendingComparison, cashFlowTrends] =
      await Promise.all([
        this.getUnifiedGoalProgress(userId),
        this.getSpendingComparison(userId, personalData, familyData),
        this.getCashFlowTrends(userId),
      ]);

    return {
      total_balance: totalBalance,
      total_monthly_income: totalIncome,
      total_monthly_expenses: totalExpenses,
      net_cash_flow: netCashFlow,
      savings_rate: savingsRate,
      personal_vs_family: {
        personal_balance: personalBalance,
        family_balance: familyData.total_balance,
        personal_spending: personalExpenses,
        family_spending: familyData.monthly_expenses,
        personal_income: personalIncome,
        family_income: familyData.monthly_income,
      },
      goal_progress: goalProgress,
      spending_comparison: spendingComparison,
      cash_flow_trends: cashFlowTrends,
    };
  }

  private async getUnifiedGoalProgress(
    userId: string
  ): Promise<UnifiedGoalProgress[]> {
    // Get personal goals
    const { data: personalGoals } = await this.supabase
      .from("financial_goals")
      .select("*")
      .eq("user_id", userId)
      .is("group_id", null)
      .eq("status", "active");

    // Get family goals
    const { data: groupMembers } = await this.supabase
      .from("family_group_members")
      .select("group_id, family_groups(name)")
      .eq("user_id", userId);

    const groupIds = (groupMembers || []).map((member) => member.group_id);

    const { data: familyGoals } =
      groupIds.length > 0
        ? await this.supabase
            .from("financial_goals")
            .select("*, family_groups(name)")
            .in("group_id", groupIds)
            .eq("status", "active")
        : { data: [] };

    const allGoals = [
      ...(personalGoals || []).map((goal: any) => ({
        ...goal,
        type: "personal" as const,
        group_name: undefined,
      })),
      ...(familyGoals || []).map((goal: any) => ({
        ...goal,
        type: "family" as const,
        group_name: goal.family_groups?.name,
      })),
    ];

    return allGoals.map((goal: any) => {
      const progress =
        goal.target_amount > 0
          ? (goal.current_amount / goal.target_amount) * 100
          : 0;
      const targetDate = new Date(goal.target_date);
      const today = new Date();
      const daysRemaining = Math.max(
        0,
        Math.ceil(
          (targetDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24)
        )
      );
      const monthlyRequired =
        daysRemaining > 0 && goal.target_amount > goal.current_amount
          ? (goal.target_amount - goal.current_amount) /
            (daysRemaining / 30.4375)
          : 0;

      return {
        id: goal.id,
        name: goal.name,
        type: goal.type,
        target_amount: goal.target_amount,
        current_amount: goal.current_amount,
        progress: Math.min(100, progress),
        days_remaining: daysRemaining,
        monthly_required: Math.max(0, monthlyRequired),
        currency: goal.currency || "USD",
        group_name: goal.group_name,
      };
    });
  }

  private async getSpendingComparison(
    userId: string,
    personalData: any,
    familyData: FamilyFinanceData
  ): Promise<SpendingComparison[]> {
    // Get personal spending by category
    const personalSpending = personalData.analytics.spending_by_category || [];

    // Get family spending by category
    const { data: groupMembers } = await this.supabase
      .from("family_group_members")
      .select("group_id")
      .eq("user_id", userId);

    const groupIds = (groupMembers || []).map((member) => member.group_id);

    const startOfMonth = new Date();
    startOfMonth.setDate(1);

    const { data: familySpending } =
      groupIds.length > 0
        ? await this.supabase
            .from("transactions")
            .select("amount, categories(name, color)")
            .in("group_id", groupIds)
            .eq("type", "expense")
            .gte("date", startOfMonth.toISOString().split("T")[0])
        : { data: [] };

    // Combine and analyze spending
    const categoryMap = new Map<
      string,
      { personal: number; family: number; color: string }
    >();

    // Process personal spending
    personalSpending.forEach((item: any) => {
      categoryMap.set(item.name, {
        personal: item.value,
        family: 0,
        color: item.color || "#8884d8",
      });
    });

    // Process family spending
    (familySpending || []).forEach((transaction: any) => {
      if (transaction.categories && transaction.categories.length > 0) {
        const category = transaction.categories[0];
        const existing = categoryMap.get(category.name) || {
          personal: 0,
          family: 0,
          color: category.color || "#8884d8",
        };
        existing.family += Math.abs(transaction.amount);
        categoryMap.set(category.name, existing);
      }
    });

    const totalSpending = Array.from(categoryMap.values()).reduce(
      (sum, cat) => sum + cat.personal + cat.family,
      0
    );

    return Array.from(categoryMap.entries())
      .map(([name, data]) => {
        const totalAmount = data.personal + data.family;
        return {
          category: name,
          personal_amount: data.personal,
          family_amount: data.family,
          total_amount: totalAmount,
          percentage:
            totalSpending > 0 ? (totalAmount / totalSpending) * 100 : 0,
          color: data.color,
        };
      })
      .sort((a, b) => b.total_amount - a.total_amount);
  }

  private async getCashFlowTrends(userId: string): Promise<CashFlowTrend[]> {
    const trends: CashFlowTrend[] = [];
    const months = 6; // Get last 6 months

    for (let i = months - 1; i >= 0; i--) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      const startOfMonth = new Date(date.getFullYear(), date.getMonth(), 1);
      const endOfMonth = new Date(date.getFullYear(), date.getMonth() + 1, 0);

      const [personalIncome, personalExpenses, familyIncome, familyExpenses] =
        await Promise.all([
          this.getPersonalMonthlyIncome(userId, startOfMonth, endOfMonth),
          this.getPersonalMonthlyExpenses(userId, startOfMonth, endOfMonth),
          this.getFamilyMonthlyIncome(userId, startOfMonth, endOfMonth),
          this.getFamilyMonthlyExpenses(userId, startOfMonth, endOfMonth),
        ]);

      trends.push({
        month: date.toLocaleDateString("en-US", {
          month: "short",
          year: "numeric",
        }),
        personal_income: personalIncome,
        personal_expenses: personalExpenses,
        family_income: familyIncome,
        family_expenses: familyExpenses,
        net_flow:
          personalIncome + familyIncome - (personalExpenses + familyExpenses),
      });
    }

    return trends;
  }

  private async getPersonalMonthlyIncome(
    userId: string,
    startDate: Date,
    endDate: Date
  ): Promise<number> {
    const { data } = await this.supabase
      .from("transactions")
      .select("amount")
      .eq("user_id", userId)
      .is("group_id", null)
      .eq("type", "income")
      .gte("date", startDate.toISOString().split("T")[0])
      .lte("date", endDate.toISOString().split("T")[0]);

    return (data || []).reduce((sum, t) => sum + (t.amount || 0), 0);
  }

  private async getPersonalMonthlyExpenses(
    userId: string,
    startDate: Date,
    endDate: Date
  ): Promise<number> {
    const { data } = await this.supabase
      .from("transactions")
      .select("amount")
      .eq("user_id", userId)
      .is("group_id", null)
      .eq("type", "expense")
      .gte("date", startDate.toISOString().split("T")[0])
      .lte("date", endDate.toISOString().split("T")[0]);

    return (data || []).reduce((sum, t) => sum + Math.abs(t.amount || 0), 0);
  }

  private async getFamilyMonthlyIncome(
    userId: string,
    startDate: Date,
    endDate: Date
  ): Promise<number> {
    const { data: groupMembers } = await this.supabase
      .from("family_group_members")
      .select("group_id")
      .eq("user_id", userId);

    const groupIds = (groupMembers || []).map((member) => member.group_id);

    if (groupIds.length === 0) return 0;

    const { data } = await this.supabase
      .from("transactions")
      .select("amount")
      .in("group_id", groupIds)
      .eq("type", "income")
      .gte("date", startDate.toISOString().split("T")[0])
      .lte("date", endDate.toISOString().split("T")[0]);

    return (data || []).reduce((sum, t) => sum + (t.amount || 0), 0);
  }

  private async getFamilyMonthlyExpenses(
    userId: string,
    startDate: Date,
    endDate: Date
  ): Promise<number> {
    const { data: groupMembers } = await this.supabase
      .from("family_group_members")
      .select("group_id")
      .eq("user_id", userId);

    const groupIds = (groupMembers || []).map((member) => member.group_id);

    if (groupIds.length === 0) return 0;

    const { data } = await this.supabase
      .from("transactions")
      .select("amount")
      .in("group_id", groupIds)
      .eq("type", "expense")
      .gte("date", startDate.toISOString().split("T")[0])
      .lte("date", endDate.toISOString().split("T")[0]);

    return (data || []).reduce((sum, t) => sum + Math.abs(t.amount || 0), 0);
  }

  // User preferences
  async getUserPreferences(userId: string): Promise<UserPreferences> {
    const { data } = await this.supabase
      .from("user_profiles")
      .select("currency")
      .eq("id", userId)
      .single();

    return {
      show_amounts: true,
      preferred_currency: data?.currency || "USD",
      default_view: "unified",
      analytics_period: "month",
    };
  }

  async updateUserPreferences(
    userId: string,
    preferences: Partial<UserPreferences>
  ): Promise<void> {
    // Only update the currency if it's provided in preferences
    if (preferences.preferred_currency) {
      await this.supabase
        .from("user_profiles")
        .update({ currency: preferences.preferred_currency })
        .eq("id", userId);
    }
    
    // Note: Other preferences (show_amounts, default_view, analytics_period) 
    // would need to be stored in a separate preferences table or JSON column
    // For now, these are handled client-side only
  }

  // Get combined insights
  async getCombinedInsights(userId: string): Promise<CombinedFinanceInsight[]> {
    try {
      const data = await this.getCombinedFinanceData(userId);
      const insights: CombinedFinanceInsight[] = [];

      // Cross-account opportunities
      if (
        data.unified.personal_vs_family.personal_balance >
        data.unified.personal_vs_family.family_balance * 2
      ) {
        insights.push({
          id: "balance_optimization",
          type: "cross_account",
          title: "Balance Optimization Opportunity",
          description:
            "Consider transferring some personal funds to family accounts for better diversification.",
          priority: "medium",
          action_required: false,
          data_points: {
            personal_balance: data.unified.personal_vs_family.personal_balance,
            family_balance: data.unified.personal_vs_family.family_balance,
          },
          created_at: new Date().toISOString(),
        });
      }

      // Savings rate insights
      if (data.unified.savings_rate < 10) {
        insights.push({
          id: "low_savings_rate",
          type: "cash_flow",
          title: "Low Savings Rate Detected",
          description: `Your combined savings rate is ${data.unified.savings_rate.toFixed(
            1
          )}%. Consider reducing expenses or increasing income.`,
          priority: "high",
          action_required: true,
          action_text: "Review Budget",
          data_points: {
            savings_rate: data.unified.savings_rate,
            net_cash_flow: data.unified.net_cash_flow,
          },
          created_at: new Date().toISOString(),
        });
      }

      return insights;
    } catch (error) {
      console.error("Error generating combined insights:", error);
      return [];
    }
  }
}

export const combinedFinanceService = new CombinedFinanceService();
