"use client";

import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from "react";
import { FinanceType, FinanceTypeCapabilities, BetaUser } from "../types";

export type DataMode = 'real' | 'mock';
import { getCapabilitiesForType } from "../config/finance-journeys";
import { createClient } from "@/shared/services/supabase/client";
import { betaService } from "../services/betaService";
import { useRouter } from "next/navigation";

interface BetaFinanceTypeContextValue {
  financeType: FinanceType;
  capabilities: FinanceTypeCapabilities;
  user: BetaUser | null;
  loading: boolean;
  dataMode: DataMode;
  setFinanceType: (type: FinanceType) => Promise<void>;
  setDataMode: (mode: DataMode) => void;
  submitFeedback: (feedback: {
    type: string;
    content?: string;
    rating?: number;
  }) => Promise<void>;
  refreshUser: () => Promise<void>;
}

const BetaFinanceTypeContext =
  createContext<BetaFinanceTypeContextValue | null>(null);

interface BetaFinanceTypeProviderProps {
  children: ReactNode;
  initialUser?: BetaUser | null;
}

export function BetaFinanceTypeProvider({
  children,
  initialUser,
}: BetaFinanceTypeProviderProps) {
  const [user, setUser] = useState<BetaUser | null>(initialUser || null);
  const [loading, setLoading] = useState(!initialUser);
  const [dataMode, setDataMode] = useState<DataMode>('mock'); // Default to mock data for safety
  const supabase = createClient();
  const router = useRouter();

  const financeType = user?.finance_type || "personal";
  const capabilities = getCapabilitiesForType(financeType);

  useEffect(() => {
    const fetchUser = async () => {
      if (initialUser) {
        setLoading(false);
        return;
      }

      try {
        const {
          data: { user: authUser },
        } = await supabase.auth.getUser();
        if (!authUser) {
          router.push("/beta/auth/login");
          return;
        }

        const betaUser = await betaService.getBetaUser(authUser.id);
        if (betaUser) {
          setUser(betaUser);
        }
      } catch (error) {
        console.error("Error fetching user:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchUser();
  }, [initialUser, supabase, router]);

  const setFinanceType = async (newType: FinanceType) => {
    if (!user) return;

    try {
      await betaService.setFinanceType(user.id, newType);
      setUser((prev) =>
        prev
          ? {
              ...prev,
              finance_type: newType,
              dashboard_preference: newType,
            }
          : null
      );
      router.refresh();
    } catch (error) {
      console.error("Error updating finance type:", error);
    }
  };

  const submitFeedback = async (feedback: {
    type: string;
    content?: string;
    rating?: number;
  }) => {
    if (!user) return;

    try {
      await betaService.submitFeedback(user.id, {
        feedback_type: feedback.type as
          | "bug"
          | "feature_request"
          | "general"
          | "rating",
        content: feedback.content,
        rating: feedback.rating,
      });

      setUser((prev) =>
        prev
          ? {
              ...prev,
              beta_feedback_count: prev.beta_feedback_count + 1,
            }
          : null
      );
    } catch (error) {
      console.error("Error submitting feedback:", error);
    }
  };

  const refreshUser = async () => {
    if (!user) return;

    try {
      const updatedUser = await betaService.getBetaUser(user.id);
      if (updatedUser) {
        setUser(updatedUser);
      }
    } catch (error) {
      console.error("Error refreshing user:", error);
    }
  };

  return (
    <BetaFinanceTypeContext.Provider
      value={{
        financeType,
        capabilities,
        user,
        loading,
        dataMode,
        setFinanceType,
        setDataMode,
        submitFeedback,
        refreshUser,
      }}
    >
      {children}
    </BetaFinanceTypeContext.Provider>
  );
}

export function useBetaFinanceType() {
  const context = useContext(BetaFinanceTypeContext);
  if (!context) {
    throw new Error(
      "useBetaFinanceType must be used within a BetaFinanceTypeProvider"
    );
  }
  return context;
}
