// Beta Program Integration Test Script
// Run this to verify the backend integration is working correctly

import { createClient } from '@/shared/services/supabase/client';
import { betaService } from './services/betaService';
import { databaseFallback } from './services/databaseFallback';

interface TestResult {
  name: string;
  passed: boolean;
  message: string;
  details?: any;
}

class BetaIntegrationTester {
  private supabase = createClient();
  private results: TestResult[] = [];

  // Add test result
  private addResult(name: string, passed: boolean, message: string, details?: any) {
    this.results.push({ name, passed, message, details });
    const status = passed ? '✅' : '❌';
    console.log(`${status} ${name}: ${message}`);
    if (details) {
      console.log('   Details:', details);
    }
  }

  // Test 1: Database Connection
  async testDatabaseConnection(): Promise<void> {
    try {
      const { data, error } = await this.supabase
        .from('user_profiles')
        .select('id')
        .limit(1);

      if (error) {
        this.addResult(
          'Database Connection',
          false,
          'Failed to connect to database',
          error.message
        );
      } else {
        this.addResult(
          'Database Connection',
          true,
          'Successfully connected to database'
        );
      }
    } catch (error) {
      this.addResult(
        'Database Connection',
        false,
        'Database connection error',
        error
      );
    }
  }

  // Test 2: Beta Schema Check
  async testBetaSchema(): Promise<void> {
    try {
      const readiness = await databaseFallback.checkDatabaseReadiness();
      
      this.addResult(
        'Beta Schema Check',
        readiness.ready,
        readiness.message,
        {
          betaTable: readiness.betaTable,
          betaFields: readiness.betaFields
        }
      );
    } catch (error) {
      this.addResult(
        'Beta Schema Check',
        false,
        'Error checking beta schema',
        error
      );
    }
  }

  // Test 3: User Authentication
  async testUserAuth(): Promise<string | null> {
    try {
      const { data: { user }, error } = await this.supabase.auth.getUser();

      if (error || !user) {
        this.addResult(
          'User Authentication',
          false,
          'No authenticated user found',
          'Please sign in to test user-specific features'
        );
        return null;
      }

      this.addResult(
        'User Authentication',
        true,
        `Authenticated as ${user.email}`,
        { userId: user.id }
      );
      
      return user.id;
    } catch (error) {
      this.addResult(
        'User Authentication',
        false,
        'Authentication error',
        error
      );
      return null;
    }
  }

  // Test 4: Beta Service - Get User
  async testBetaServiceGetUser(userId: string): Promise<void> {
    try {
      const betaUser = await betaService.getBetaUser(userId);

      if (betaUser) {
        this.addResult(
          'Beta Service - Get User',
          true,
          'Successfully retrieved beta user data',
          {
            financeType: betaUser.finance_type,
            betaUser: betaUser.beta_user,
            onboardingCompleted: betaUser.onboarding_completed
          }
        );
      } else {
        this.addResult(
          'Beta Service - Get User',
          false,
          'Failed to retrieve beta user data'
        );
      }
    } catch (error) {
      this.addResult(
        'Beta Service - Get User',
        false,
        'Error in beta service get user',
        error
      );
    }
  }

  // Test 5: Beta Service - Update User
  async testBetaServiceUpdateUser(userId: string): Promise<void> {
    try {
      await betaService.updateBetaUser(userId, {
        finance_type: 'personal',
        onboarding_completed: true
      });

      this.addResult(
        'Beta Service - Update User',
        true,
        'Successfully updated beta user data'
      );
    } catch (error) {
      this.addResult(
        'Beta Service - Update User',
        false,
        'Error updating beta user data',
        error
      );
    }
  }

  // Test 6: Feedback Submission
  async testFeedbackSubmission(userId: string): Promise<void> {
    try {
      await betaService.submitFeedback(userId, {
        feedback_type: 'general',
        content: 'Integration test feedback',
        rating: 5
      });

      this.addResult(
        'Feedback Submission',
        true,
        'Successfully submitted test feedback'
      );
    } catch (error) {
      this.addResult(
        'Feedback Submission',
        false,
        'Error submitting feedback',
        error
      );
    }
  }

  // Test 7: Service Factory
  async testServiceFactory(): Promise<void> {
    try {
      // Import service factory dynamically to avoid circular dependencies
      const { getDataModeInfo } = await import('./services/serviceFactory');
      
      const mockInfo = getDataModeInfo('mock');
      const realInfo = getDataModeInfo('real');

      const mockValid = mockInfo.mode === 'mock' && mockInfo.description.includes('mock');
      const realValid = realInfo.mode === 'real' && realInfo.description.includes('real');

      this.addResult(
        'Service Factory',
        mockValid && realValid,
        'Service factory correctly identifies data modes',
        { mockInfo, realInfo }
      );
    } catch (error) {
      this.addResult(
        'Service Factory',
        false,
        'Error testing service factory',
        error
      );
    }
  }

  // Run all tests
  async runAllTests(): Promise<TestResult[]> {
    console.log('🧪 Starting Beta Program Integration Tests...\n');

    // Test 1: Database Connection
    await this.testDatabaseConnection();

    // Test 2: Beta Schema Check
    await this.testBetaSchema();

    // Test 3: User Authentication
    const userId = await this.testUserAuth();

    // User-specific tests (only if authenticated)
    if (userId) {
      await this.testBetaServiceGetUser(userId);
      await this.testBetaServiceUpdateUser(userId);
      await this.testFeedbackSubmission(userId);
    }

    // Test 7: Service Factory
    await this.testServiceFactory();

    // Summary
    console.log('\n📊 Test Summary:');
    const passed = this.results.filter(r => r.passed).length;
    const total = this.results.length;
    console.log(`${passed}/${total} tests passed`);

    if (passed === total) {
      console.log('🎉 All tests passed! Beta integration is working correctly.');
    } else {
      console.log('⚠️  Some tests failed. Check the details above.');
    }

    return this.results;
  }

  // Get summary
  getSummary(): { passed: number; total: number; success: boolean } {
    const passed = this.results.filter(r => r.passed).length;
    const total = this.results.length;
    return {
      passed,
      total,
      success: passed === total
    };
  }
}

// Export for use in components or pages
export { BetaIntegrationTester };

// Function to run tests from browser console
export async function runBetaIntegrationTests(): Promise<TestResult[]> {
  const tester = new BetaIntegrationTester();
  return await tester.runAllTests();
}

// Default export
export default BetaIntegrationTester;
