"use client";

import {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
  useCallback,
} from "react";
import { createClient } from "@/shared/services/supabase/client";
import { toast } from "sonner";

export type UserSettings = {
  currency: string;
  language: string;
  // Add other settings as needed
};

export type UserSettingsContextType = {
  settings: UserSettings;
  updateSettings: (newSettings: Partial<UserSettings>) => Promise<void>;
  loading: boolean;
  refreshSettings: () => Promise<void>; // Added refresh function
};

const UserSettingsContext = createContext<UserSettingsContextType | undefined>(
  undefined
);

export const UserSettingsProvider = ({ children }: { children: ReactNode }) => {
  const [settings, setSettings] = useState<UserSettings>({ currency: "USD", language: "en" });
  const [loading, setLoading] = useState(true);

  const fetchUserSettings = useCallback(async () => {
    console.log("Fetching user settings...");
    setLoading(true);
    try {
      const supabase = createClient();
      const {
        data: { user },
      } = await supabase.auth.getUser();

      console.log("User object in fetchUserSettings:", user);

      if (!user) {
        // Don't redirect here, let pages handle auth. Toast is okay.
        toast.info("Please login to load your personalized settings.");
        setSettings({ currency: "USD", language: "en" }); // Fallback to default
        return;
      }

      const { data, error } = await supabase
        .from("user_profiles")
        .select("currency, language")
        .eq("id", user.id)
        .single();

      if (error && error.code !== "PGRST116") {
        // PGRST116: single row not found
        throw error;
      }

      if (data) {
        console.log("Fetched settings:", data);
        setSettings({
          currency: data.currency || "USD",
          language: data.language || "en"
        });
      } else {
        console.log("No settings found for user, using default.");
        setSettings({ currency: "USD", language: "en" });
      }
    } catch (error: unknown) {
      console.error("Raw error object in fetchUserSettings:", error);

      const errorDetails: Record<string, unknown> = {
        isErrorInstance: error instanceof Error,
      };

      // Safely access error properties
      if (error && typeof error === "object") {
        const typedError = error as Record<string, unknown>;
        errorDetails.name = typedError.name;
        errorDetails.message = typedError.message;
        errorDetails.stack = typedError.stack;
        errorDetails.code = typedError.code; // For Supabase errors
        errorDetails.details = typedError.details; // For Supabase errors
        errorDetails.hint = typedError.hint; // For Supabase errors
      }

      try {
        errorDetails.stringified = JSON.stringify(error);
      } catch {
        errorDetails.stringified = "Could not stringify error";
      }

      try {
        // Attempt to stringify with own properties, including non-enumerable ones
        errorDetails.stringifiedWithProps = JSON.stringify(
          error,
          Object.getOwnPropertyNames(error)
        );
      } catch {
        errorDetails.stringifiedWithProps =
          "Could not stringify error with own props";
      }

      errorDetails.keys =
        error && typeof error === "object" ? Object.keys(error) : [];
      errorDetails.ownPropertyNames =
        error && typeof error === "object"
          ? Object.getOwnPropertyNames(error)
          : [];

      console.error("Detailed error info in fetchUserSettings:", errorDetails);

      let toastMessage = "Failed to load user settings.";
      if (errorDetails.message) {
        toastMessage = `Failed to load user settings: ${errorDetails.message}`;
      } else if (typeof error === "string") {
        toastMessage = `Failed to load user settings: ${error}`;
      } else if (
        errorDetails.stringified &&
        errorDetails.stringified !== "{}"
      ) {
        toastMessage = `Failed to load user settings: ${errorDetails.stringified}`;
      }

      toast.error(toastMessage + " Using defaults.");
      setSettings({ currency: "USD", language: "en" }); // Fallback to default on error
    } finally {
      setLoading(false);
      console.log(
        "Fetching settings finished. Loading:",
        false,
        "Settings will be updated"
      );
    }
  }, []); // Remove settings dependency to prevent infinite loop

  useEffect(() => {
    fetchUserSettings();
  }, [fetchUserSettings]);

  const updateSettings = async (newSettings: Partial<UserSettings>) => {
    console.log("Updating settings with:", newSettings);
    setLoading(true);
    try {
      const supabase = createClient();
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        toast.error("You must be logged in to update settings.");
        return;
      }

      // Ensure we only try to update fields that are part of UserSettings
      const validUpdates: Partial<UserSettings> = {};
      if (newSettings.currency !== undefined) {
        validUpdates.currency = newSettings.currency;
      }
      if (newSettings.language !== undefined) {
        validUpdates.language = newSettings.language;
      }
      // Add other updatable settings here if any

      if (Object.keys(validUpdates).length === 0) {
        toast.info("No valid settings to update.");
        setLoading(false);
        return;
      }

      const { data: updatedProfile, error } = await supabase
        .from("user_profiles")
        .update(validUpdates) // Use validated updates
        .eq("id", user.id)
        .select("currency, language") // Select the fields that were updated
        .single();

      if (error) throw error;

      if (updatedProfile) {
        console.log("Supabase updated, new profile data:", updatedProfile);
        setSettings((prevSettings) => ({
          ...prevSettings,
          currency: updatedProfile.currency || prevSettings.currency,
          language: updatedProfile.language || prevSettings.language,
        }));
        toast.success("Settings updated successfully!");
      } else {
        // This case should ideally not happen if update was successful and returned data
        toast.info(
          "Settings updated, but could not fetch confirmation. Refreshing data."
        );
        await fetchUserSettings(); // Re-fetch to be sure
      }
    } catch (error) {
      console.error("Error updating settings:", error);
      toast.error("Failed to update settings.");
    } finally {
      setLoading(false);
      console.log(
        "Update settings finished. Loading:",
        false,
        "Current settings:",
        settings
      );
    }
  };

  // Expose fetchUserSettings as refreshSettings
  const refreshSettings = useCallback(async () => {
    await fetchUserSettings();
  }, [fetchUserSettings]);

  // Allow children to render even while loading, they can handle their own loading states

  return (
    <UserSettingsContext.Provider
      value={{ settings, updateSettings, loading, refreshSettings }}
    >
      {children}
    </UserSettingsContext.Provider>
  );
};

export const useUserSettings = () => {
  const context = useContext(UserSettingsContext);
  if (context === undefined) {
    throw new Error(
      "useUserSettings must be used within a UserSettingsProvider"
    );
  }
  return context;
};
