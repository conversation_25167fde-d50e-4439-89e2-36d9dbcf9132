"use client";

import { LocaleSwitcher } from "lingo.dev/react/client";

interface LanguageSwitcherProps {
  variant?: "icon" | "full";
  className?: string;
}

export function LanguageSwitcher({
  variant = "icon",
  className = ""
}: LanguageSwitcherProps) {
  // Always use Lingo's built-in LocaleSwitcher for proper functionality
  return (
    <div className={className}>
      <LocaleSwitcher
        locales={["en", "nl", "de", "fr"]}
        className={
          variant === "full"
            ? "flex items-center gap-2 px-3 py-2 text-sm rounded-md border border-input bg-background hover:bg-accent hover:text-accent-foreground"
            : "flex items-center gap-2 px-2 py-1 text-sm rounded-md hover:bg-accent hover:text-accent-foreground"
        }
      />
    </div>
  );
}
