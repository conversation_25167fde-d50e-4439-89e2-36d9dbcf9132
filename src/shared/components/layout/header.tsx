"use client";

import Link from "next/link";
import { useRouter, usePathname } from "next/navigation";
import { createClient } from "@/shared/services/supabase/client";
import { But<PERSON> } from "@/shared/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/shared/components/ui/dropdown-menu";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@/shared/components/ui/avatar";
import { useToast } from "@/shared/hooks/use-toast";
import { LanguageSwitcher } from "@/shared/components/ui/language-switcher";
import {
  LogOut,
  User,
  Moon,
  Sun,
  Menu,
  ChevronDown,
  Users,
  Home,
} from "lucide-react";
import { useTheme } from "next-themes";
import { useEffect, useState } from "react";
import { useMobileMenu } from "@/shared/contexts/mobile-menu-context";

function getInitials(fullName?: string, email?: string): string {
  if (fullName) {
    const names = fullName.split(" ");
    if (names.length > 1) {
      return `${names[0][0]}${names[names.length - 1][0]}`.toUpperCase();
    }
    return names[0].substring(0, 2).toUpperCase();
  }
  if (email) {
    const emailParts = email.split("@")[0];
    const nameParts = emailParts.split(/[._-]/);
    return nameParts.length > 1
      ? nameParts
          .map((n) => n[0])
          .join("")
          .toUpperCase()
          .substring(0, 2)
      : emailParts.substring(0, 2).toUpperCase();
  }
  return "U";
}

export function Header() {
  const { toggle } = useMobileMenu();
  const router = useRouter();
  const pathname = usePathname();
  const { toast } = useToast();
  const { theme, setTheme } = useTheme();
  const [user, setUser] = useState<{
    email?: string;
    user_metadata?: {
      full_name?: string;
      avatar_url?: string;
    };
  } | null>(null);
  const [userProfile, setUserProfile] = useState<{
    avatar_url?: string;
    full_name?: string;
  } | null>(null);

  useEffect(() => {
    const getUser = async () => {
      const supabase = createClient();
      const {
        data: { user },
      } = await supabase.auth.getUser();
      setUser(user);

      // Also get user profile data for avatar
      if (user) {
        const { data: profile } = await supabase
          .from("user_profiles")
          .select("avatar_url, full_name")
          .eq("id", user.id)
          .single();

        if (profile) {
          setUserProfile({
            avatar_url: profile.avatar_url || undefined,
            full_name: profile.full_name || undefined,
          });
        }
      }
    };
    getUser();

    // Listen for storage changes to refresh avatar
    const handleStorageChange = () => {
      getUser();
    };

    window.addEventListener("avatar-updated", handleStorageChange);
    return () =>
      window.removeEventListener("avatar-updated", handleStorageChange);
  }, []);

  const handleLogout = async () => {
    const supabase = createClient();
    const { error } = await supabase.auth.signOut();

    if (error) {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
      return;
    }

    router.push("/");
    router.refresh();
  };

  // Determine current mode based on pathname
  const getCurrentMode = () => {
    if (pathname.startsWith("/dashboard/personal")) return "Personal Finance";
    if (pathname.startsWith("/dashboard/family")) return "Family Finance";
    return "Dashboard";
  };

  // Show mode switcher only when in finance modes
  const showModeSwitcher =
    pathname !== "/dashboard" &&
    (pathname.startsWith("/dashboard/personal") ||
      pathname.startsWith("/dashboard/family") ||
      pathname.startsWith("/dashboard/transactions") ||
      pathname.startsWith("/dashboard/subscriptions") ||
      pathname.startsWith("/dashboard/budgets") ||
      pathname.startsWith("/dashboard/categories") ||
      pathname.startsWith("/dashboard/goals") ||
      pathname.startsWith("/dashboard/analytics") ||
      pathname.startsWith("/dashboard/accounts") ||
      pathname.startsWith("/dashboard/invitations") ||
      pathname.startsWith("/dashboard/settings"));

  return (
    <header className="sticky top-0 z-40 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="flex h-16 items-center justify-between px-4 md:px-6">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="icon"
            className="md:hidden"
            onClick={toggle}
            aria-label="Toggle menu"
          >
            <Menu className="h-5 w-5" />
          </Button>

          <div className="flex items-center gap-4">
            <Link
              href="/dashboard"
              className="flex items-center gap-2 hover:opacity-80 transition-opacity"
            >
              <h1 className="text-lg font-semibold">Budget Tracker</h1>
            </Link>

            {showModeSwitcher && (
              <>
                <div className="hidden md:block w-px h-6 bg-border" />
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="flex items-center gap-2">
                      {pathname.startsWith("/dashboard/personal") && (
                        <User className="h-4 w-4" />
                      )}
                      {pathname.startsWith("/dashboard/family") && (
                        <Users className="h-4 w-4" />
                      )}
                      <span className="hidden sm:inline">
                        {getCurrentMode()}
                      </span>
                      <ChevronDown className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="start" className="w-48">
                    <DropdownMenuLabel>Switch Mode</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <Link href="/dashboard" passHref>
                      <DropdownMenuItem>
                        <Home className="mr-2 h-4 w-4" />
                        <span>Dashboard Home</span>
                      </DropdownMenuItem>
                    </Link>
                    <Link href="/dashboard/personal" passHref>
                      <DropdownMenuItem>
                        <User className="mr-2 h-4 w-4" />
                        <span>Personal Finance</span>
                      </DropdownMenuItem>
                    </Link>
                    <Link href="/dashboard/family" passHref>
                      <DropdownMenuItem>
                        <Users className="mr-2 h-4 w-4" />
                        <span>Family Finance</span>
                      </DropdownMenuItem>
                    </Link>
                  </DropdownMenuContent>
                </DropdownMenu>
              </>
            )}
          </div>
        </div>

        <div className="flex items-center gap-4">
          <LanguageSwitcher variant="full" />
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
            aria-label="Toggle theme"
          >
            <Sun className="h-5 w-5 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
            <Moon className="absolute h-5 w-5 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
            <span className="sr-only">Toggle theme</span>
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                <Avatar className="h-8 w-8">
                  <AvatarImage
                    src={
                      userProfile?.avatar_url || user?.user_metadata?.avatar_url
                    }
                  />
                  <AvatarFallback>
                    {getInitials(
                      userProfile?.full_name || user?.user_metadata?.full_name,
                      user?.email
                    )}
                  </AvatarFallback>
                </Avatar>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56" align="end" forceMount>
              <DropdownMenuLabel className="font-normal">
                <div className="flex flex-col space-y-1">
                  <p className="text-sm font-medium leading-none">
                    {userProfile?.full_name ||
                      user?.user_metadata?.full_name ||
                      "User"}
                  </p>
                  <p className="text-xs leading-none text-muted-foreground">
                    {user?.email}
                  </p>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <Link href="/dashboard/settings" passHref>
                <DropdownMenuItem>
                  <User className="mr-2 h-4 w-4" />
                  <span>Profile</span>
                </DropdownMenuItem>
              </Link>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleLogout}>
                <LogOut className="mr-2 h-4 w-4" />
                <span>Log out</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  );
}
