import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { ThemeProvider, QueryProvider } from "@/shared/components";
import { Toaster as SonnerToaster } from "sonner";
import { MobileMenuProvider, UserSettingsProvider } from "@/shared/contexts";
import { LingoProvider, loadDictionary } from "lingo.dev/react/rsc";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Budget Tracker",
  description: "Modern budget tracking and expense management application",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <LingoProvider loadDictionary={(locale) => loadDictionary(locale)}>
      <html lang="en" suppressHydrationWarning>
        <body className={inter.className}>
          <ThemeProvider
            attribute="class"
            defaultTheme="system"
            enableSystem
            disableTransitionOnChange
          >
            <QueryProvider>
              <UserSettingsProvider>
                <MobileMenuProvider>
                  {children}
                  <SonnerToaster richColors position="top-right" />
                </MobileMenuProvider>
              </UserSettingsProvider>
            </QueryProvider>
          </ThemeProvider>
        </body>
      </html>
    </LingoProvider>
  );
}
