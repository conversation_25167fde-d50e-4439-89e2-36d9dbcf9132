"use client";

import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/shared/components/ui/card";
import { <PERSON><PERSON> } from "@/shared/components/ui/button";
import { Badge } from "@/shared/components/ui/badge";
import {
  Brain,
  Sparkles,
  TrendingUp,
  Target,
  Lightbulb,
  AlertTriangle,
  CheckCircle,
  DollarSign,
  Zap,
  Eye,
  Settings,
} from "lucide-react";

export default function AIInsightsPage() {

  const [insights] = useState([
    {
      id: "1",
      type: "optimization",
      priority: "high",
      title: "Optimize Your Vacation Fund Strategy",
      description:
        "Based on your spending patterns, you could reach your vacation goal 2 months faster by redirecting $150/month from dining out.",
      impact: "$2,400 faster goal achievement",
      confidence: 94,
      category: "Goals",
      actionable: true,
      timeframe: "2 months faster",
      icon: "Target",
    },
    {
      id: "2",
      type: "alert",
      priority: "medium",
      title: "Subscription Spending Creep Detected",
      description:
        "Your subscription costs have increased 23% over the last 6 months. We found $67/month in potentially unused services.",
      impact: "$804 annual savings potential",
      confidence: 87,
      category: "Expenses",
      actionable: true,
      timeframe: "Immediate",
      icon: "AlertTriangle",
    },
    {
      id: "3",
      type: "achievement",
      priority: "low",
      title: "Emergency Fund Milestone Reached",
      description:
        "Congratulations! You've successfully built a 4-month emergency fund. Consider increasing contributions to reach 6 months.",
      impact: "33% above recommended minimum",
      confidence: 100,
      category: "Savings",
      actionable: false,
      timeframe: "Achieved",
      icon: "CheckCircle",
    },
    {
      id: "4",
      type: "prediction",
      priority: "high",
      title: "Budget Overspend Risk in Food Category",
      description:
        "Current spending pace suggests you'll exceed your food budget by $180 this month. Consider meal planning to stay on track.",
      impact: "$180 potential overspend",
      confidence: 78,
      category: "Budgets",
      actionable: true,
      timeframe: "2 weeks",
      icon: "TrendingUp",
    },
    {
      id: "5",
      type: "opportunity",
      priority: "medium",
      title: "Investment Rebalancing Opportunity",
      description:
        "Your portfolio has drifted from target allocation. Rebalancing could improve risk-adjusted returns by 0.3% annually.",
      impact: "0.3% improved returns",
      confidence: 82,
      category: "Investments",
      actionable: true,
      timeframe: "1 week",
      icon: "Lightbulb",
    },
  ]);

  const getInsightIcon = (iconName: string) => {
    const iconMap = {
      Target: Target,
      AlertTriangle: AlertTriangle,
      CheckCircle: CheckCircle,
      TrendingUp: TrendingUp,
      Lightbulb: Lightbulb,
    };
    const Icon = iconMap[iconName as keyof typeof iconMap] || Brain;
    return <Icon className="h-5 w-5" />;
  };

  const getInsightColor = (type: string, priority: string) => {
    if (priority === "high") {
      return "border-red-200 bg-red-50";
    }
    switch (type) {
      case "optimization":
        return "border-blue-200 bg-blue-50";
      case "alert":
        return "border-yellow-200 bg-yellow-50";
      case "achievement":
        return "border-green-200 bg-green-50";
      case "prediction":
        return "border-purple-200 bg-purple-50";
      case "opportunity":
        return "border-orange-200 bg-orange-50";
      default:
        return "border-gray-200 bg-gray-50";
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "bg-red-100 text-red-800";
      case "medium":
        return "bg-yellow-100 text-yellow-800";
      case "low":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const highPriorityInsights = insights.filter(
    (insight) => insight.priority === "high"
  );
  const actionableInsights = insights.filter((insight) => insight.actionable);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <div className="flex items-center gap-2 mb-2">
            <h1 className="text-3xl font-bold tracking-tight">AI Insights</h1>
            <Badge className="bg-gradient-to-r from-purple-600 to-blue-600 text-white">
              <Sparkles className="w-3 h-3 mr-1" />
              New Beta Feature
            </Badge>
          </div>
          <p className="text-muted-foreground">
            Intelligent financial recommendations powered by machine learning
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline">
            <Settings className="w-4 h-4 mr-2" />
            Customize
          </Button>
          <Button>
            <Brain className="w-4 h-4 mr-2" />
            Refresh Insights
          </Button>
        </div>
      </div>

      {/* AI Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="border-l-4 border-purple-500">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <Brain className="h-5 w-5 text-purple-500" />
              Total Insights
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold">{insights.length}</p>
            <p className="text-sm text-muted-foreground mt-1">
              Active recommendations
            </p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-red-500">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <Zap className="h-5 w-5 text-red-500" />
              High Priority
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold">{highPriorityInsights.length}</p>
            <p className="text-sm text-muted-foreground mt-1">
              Require attention
            </p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-blue-500">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <Target className="h-5 w-5 text-blue-500" />
              Actionable
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold">{actionableInsights.length}</p>
            <p className="text-sm text-muted-foreground mt-1">
              Ready to implement
            </p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-green-500">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <DollarSign className="h-5 w-5 text-green-500" />
              Potential Savings
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold">$3,204</p>
            <p className="text-sm text-muted-foreground mt-1">If all applied</p>
          </CardContent>
        </Card>
      </div>

      {/* High Priority Insights */}
      <Card className="border-l-4 border-red-500">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            High Priority Insights
          </CardTitle>
          <CardDescription>
            These insights require immediate attention
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {highPriorityInsights.map((insight) => (
              <Card
                key={insight.id}
                className={`border ${getInsightColor(
                  insight.type,
                  insight.priority
                )}`}
              >
                <CardContent className="pt-4">
                  <div className="flex items-start gap-4">
                    <div className="p-3 rounded-full bg-white/80">
                      {getInsightIcon(insight.icon)}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-start justify-between mb-2">
                        <div>
                          <h4 className="font-semibold">{insight.title}</h4>
                          <div className="flex items-center gap-2 mt-1">
                            <Badge
                              variant="outline"
                              className={`text-xs ${getPriorityColor(
                                insight.priority
                              )}`}
                            >
                              {insight.priority} priority
                            </Badge>
                            <Badge variant="outline" className="text-xs">
                              {insight.category}
                            </Badge>
                            <Badge variant="outline" className="text-xs">
                              {insight.confidence}% confidence
                            </Badge>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-sm font-semibold text-green-600">
                            {insight.impact}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            {insight.timeframe}
                          </p>
                        </div>
                      </div>
                      <p className="text-sm text-gray-700 mb-3">
                        {insight.description}
                      </p>
                      {insight.actionable && (
                        <div className="flex gap-2">
                          <Button size="sm" variant="default">
                            Take Action
                          </Button>
                          <Button size="sm" variant="outline">
                            Learn More
                          </Button>
                          <Button size="sm" variant="ghost">
                            Dismiss
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* All Insights */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5" />
            All AI Insights
          </CardTitle>
          <CardDescription>
            Complete list of personalized financial recommendations
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {insights.map((insight) => (
              <Card
                key={insight.id}
                className={`border ${getInsightColor(
                  insight.type,
                  insight.priority
                )}`}
              >
                <CardContent className="pt-4">
                  <div className="flex items-start gap-4">
                    <div className="p-2 rounded-full bg-white/80">
                      {getInsightIcon(insight.icon)}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-start justify-between mb-2">
                        <div>
                          <h4 className="font-semibold text-sm">
                            {insight.title}
                          </h4>
                          <div className="flex items-center gap-2 mt-1">
                            <Badge
                              variant="outline"
                              className={`text-xs ${getPriorityColor(
                                insight.priority
                              )}`}
                            >
                              {insight.priority}
                            </Badge>
                            <Badge variant="outline" className="text-xs">
                              {insight.category}
                            </Badge>
                            <Badge variant="outline" className="text-xs">
                              <Brain className="w-3 h-3 mr-1" />
                              {insight.confidence}%
                            </Badge>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-xs font-semibold text-green-600">
                            {insight.impact}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            {insight.timeframe}
                          </p>
                        </div>
                      </div>
                      <p className="text-xs text-gray-700 mb-3">
                        {insight.description}
                      </p>
                      {insight.actionable && (
                        <div className="flex gap-2">
                          <Button size="sm" variant="outline">
                            View Details
                          </Button>
                          <Button size="sm" variant="ghost">
                            <Eye className="w-3 h-3 mr-1" />
                            Mark as Seen
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* AI Features */}
      <Card className="border-l-4 border-purple-500 bg-purple-50/50">
        <CardContent className="pt-6">
          <div className="flex items-start gap-3">
            <div className="p-2 rounded-full bg-purple-100">
              <Sparkles className="h-5 w-5 text-purple-600" />
            </div>
            <div>
              <h3 className="font-semibold text-purple-900">
                AI-Powered Financial Intelligence
              </h3>
              <p className="text-sm text-purple-700 mt-1">
                Our machine learning algorithms analyze your spending patterns,
                financial goals, and market conditions to provide personalized
                insights that help you make smarter financial decisions.
              </p>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                <div className="bg-white rounded-lg p-3 border">
                  <h4 className="font-medium text-purple-900 text-sm flex items-center gap-1">
                    <Brain className="h-3 w-3" />
                    Pattern Recognition
                  </h4>
                  <p className="text-xs text-purple-700 mt-1">
                    Identifies trends and anomalies in your financial behavior
                  </p>
                </div>
                <div className="bg-white rounded-lg p-3 border">
                  <h4 className="font-medium text-purple-900 text-sm flex items-center gap-1">
                    <Target className="h-3 w-3" />
                    Goal Optimization
                  </h4>
                  <p className="text-xs text-purple-700 mt-1">
                    Suggests the most efficient paths to achieve your financial
                    goals
                  </p>
                </div>
                <div className="bg-white rounded-lg p-3 border">
                  <h4 className="font-medium text-purple-900 text-sm flex items-center gap-1">
                    <Lightbulb className="h-3 w-3" />
                    Proactive Alerts
                  </h4>
                  <p className="text-xs text-purple-700 mt-1">
                    Early warnings about potential financial risks and
                    opportunities
                  </p>
                </div>
              </div>
              <div className="mt-4">
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="text-xs">
                    Real-time Analysis
                  </Badge>
                  <Badge variant="outline" className="text-xs">
                    Personalized Recommendations
                  </Badge>
                  <Badge variant="outline" className="text-xs">
                    Continuous Learning
                  </Badge>
                  <Badge variant="outline" className="text-xs">
                    Privacy Protected
                  </Badge>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
