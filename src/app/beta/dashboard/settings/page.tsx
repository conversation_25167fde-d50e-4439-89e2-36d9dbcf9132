"use client";

import React, { useState } from "react";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/shared/components/ui/card";
import { <PERSON><PERSON> } from "@/shared/components/ui/button";
import { Badge } from "@/shared/components/ui/badge";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/shared/components/ui/alert-dialog";
import { Label } from "@/shared/components/ui/label";
import {
  Settings,
  RotateCcw,
  Download,
  Sparkles,
  Shield,
  AlertTriangle,
} from "lucide-react";
import { useBetaFinanceType } from "@/beta/contexts/BetaFinanceTypeContext";
import { betaService } from "@/beta/services/betaService";
import { useToast } from "@/shared/hooks/use-toast";
import { useRouter } from "next/navigation";
import { DataModeToggle } from "@/beta/components/DataModeToggle";

export default function BetaSettingsPage() {
  const { user, financeType, refreshUser } = useBetaFinanceType();
  const { toast } = useToast();
  const router = useRouter();
  const [loading, setLoading] = useState(false);

  const handleResetOnboarding = async () => {
    if (!user) return;

    try {
      setLoading(true);

      // Clear all onboarding-related localStorage
      const onboardingKeys = [
        "beta-onboarding-data",
        "beta-onboarding-step",
        "beta-onboarding-completed",
        "beta-onboarding-final-data",
        "beta-onboarding-completed-at",
      ];

      onboardingKeys.forEach((key) => {
        if (typeof window !== "undefined") {
          localStorage.removeItem(key);
        }
      });

      // Reset onboarding in the service
      await betaService.resetOnboarding(user.id);

      // Refresh user context to get updated state
      await refreshUser();

      toast({
        title: "Onboarding Reset Successfully",
        description:
          "You can now choose a different finance type. Redirecting to onboarding...",
      });

      // Small delay to show the toast, then redirect
      setTimeout(() => {
        router.push("/beta/onboarding");
      }, 1500);
    } catch (error) {
      console.error("Reset onboarding error:", error);
      toast({
        title: "Reset Failed",
        description:
          "There was an error resetting your onboarding. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleResetAllBetaData = async () => {
    if (!user) return;

    try {
      setLoading(true);

      // Clear ALL beta-related localStorage
      const allBetaKeys = [
        "beta_users",
        "beta_feedback",
        "beta-onboarding-data",
        "beta-onboarding-step",
        "beta-onboarding-completed",
        "beta-onboarding-final-data",
        "beta-onboarding-completed-at",
      ];

      allBetaKeys.forEach((key) => {
        if (typeof window !== "undefined") {
          localStorage.removeItem(key);
        }
      });

      // Reset all beta data in the service
      await betaService.resetBetaData(user.id);

      // Refresh user context
      await refreshUser();

      toast({
        title: "All Beta Data Reset",
        description:
          "All your beta program data has been reset. Redirecting to onboarding...",
      });

      // Redirect to onboarding
      setTimeout(() => {
        router.push("/beta/onboarding");
      }, 1500);
    } catch (error) {
      console.error("Reset all data error:", error);
      toast({
        title: "Reset Failed",
        description:
          "There was an error resetting your beta data. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const exportBetaData = () => {
    try {
      const allBetaKeys = [
        "beta_users",
        "beta_feedback",
        "beta-onboarding-data",
        "beta-onboarding-final-data",
      ];

      const exportData: Record<string, unknown> = {};

      allBetaKeys.forEach((key) => {
        const data = localStorage.getItem(key);
        if (data) {
          try {
            exportData[key] = JSON.parse(data);
          } catch {
            exportData[key] = data;
          }
        }
      });

      const dataStr = JSON.stringify(exportData, null, 2);
      const dataBlob = new Blob([dataStr], { type: "application/json" });
      const url = URL.createObjectURL(dataBlob);

      const link = document.createElement("a");
      link.href = url;
      link.download = `beta-data-export-${
        new Date().toISOString().split("T")[0]
      }.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      toast({
        title: "Data Exported",
        description: "Your beta data has been exported successfully.",
      });
    } catch (error) {
      console.error("Export error:", error);
      toast({
        title: "Export Failed",
        description: "There was an error exporting your data.",
        variant: "destructive",
      });
    }
  };

  if (!user) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading your settings...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Beta Settings</h1>
          <p className="text-muted-foreground">
            Manage your beta program settings and preferences
          </p>
        </div>
        <Badge
          variant="secondary"
          className="bg-gradient-to-r from-purple-600 to-blue-600 text-white"
        >
          <Sparkles className="w-3 h-3 mr-1" />
          Beta Program
        </Badge>
      </div>

      {/* Data Source Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5 text-blue-500" />
            Data Source Configuration
          </CardTitle>
          <CardDescription>
            Switch between mock data for testing and real database for production use
          </CardDescription>
        </CardHeader>
        <CardContent>
          <DataModeToggle showDescription={true} />
        </CardContent>
      </Card>

      {/* Beta Program Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5 text-purple-500" />
            Beta Program Status
          </CardTitle>
          <CardDescription>
            Your current beta program enrollment and progress
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label className="text-sm font-medium">Finance Type</Label>
              <p className="text-lg font-semibold">
                {financeType === "personal" && "Personal Finance"}
                {financeType === "family" && "Family Finance"}
                {financeType === "combined" && "Combined Finance"}
              </p>
            </div>
            <div>
              <Label className="text-sm font-medium">Onboarding Status</Label>
              <p className="text-lg font-semibold flex items-center gap-2">
                {user.onboarding_completed ? (
                  <>
                    <Badge
                      variant="default"
                      className="bg-green-100 text-green-800"
                    >
                      Completed
                    </Badge>
                  </>
                ) : (
                  <>
                    <Badge
                      variant="outline"
                      className="bg-orange-100 text-orange-800"
                    >
                      In Progress
                    </Badge>
                  </>
                )}
              </p>
            </div>
            <div>
              <Label className="text-sm font-medium">Beta Joined</Label>
              <p className="text-sm text-muted-foreground">
                {user.beta_joined_at
                  ? new Date(user.beta_joined_at).toLocaleDateString()
                  : "N/A"}
              </p>
            </div>
            <div>
              <Label className="text-sm font-medium">Feedback Submitted</Label>
              <p className="text-sm text-muted-foreground">
                {user.beta_feedback_count || 0} submissions
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Onboarding Management */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <RotateCcw className="h-5 w-5 text-blue-500" />
            Onboarding Management
          </CardTitle>
          <CardDescription>
            Reset your onboarding to try different finance types or start over
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-start gap-3 p-4 bg-blue-50 rounded-lg border">
            <RotateCcw className="h-5 w-5 text-blue-600 mt-0.5" />
            <div className="flex-1">
              <h4 className="font-medium text-blue-900">Reset Onboarding</h4>
              <p className="text-sm text-blue-700 mt-1">
                This will reset your onboarding progress and allow you to choose
                a different finance type. Your existing data will be preserved.
              </p>
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button variant="outline" className="mt-3" disabled={loading}>
                    {loading ? "Resetting..." : "Reset Onboarding"}
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Reset Onboarding?</AlertDialogTitle>
                    <AlertDialogDescription>
                      This will reset your onboarding progress and return you to
                      the finance type selection. Your existing financial data
                      will not be affected. You can choose a different finance
                      type and go through the setup process again.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={handleResetOnboarding}
                      disabled={loading}
                    >
                      {loading ? "Resetting..." : "Reset Onboarding"}
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Data Management */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5 text-gray-500" />
            Data Management
          </CardTitle>
          <CardDescription>
            Export or reset your beta program data
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Export Data */}
            <div className="flex items-start gap-3 p-4 bg-gray-50 rounded-lg border">
              <Download className="h-5 w-5 text-gray-600 mt-0.5" />
              <div className="flex-1">
                <h4 className="font-medium text-gray-900">Export Beta Data</h4>
                <p className="text-sm text-gray-600 mt-1">
                  Download all your beta program data as a JSON file for backup
                  or analysis.
                </p>
                <Button
                  variant="outline"
                  size="sm"
                  className="mt-3"
                  onClick={exportBetaData}
                >
                  <Download className="w-4 h-4 mr-2" />
                  Export Data
                </Button>
              </div>
            </div>

            {/* Reset All Data */}
            <div className="flex items-start gap-3 p-4 bg-red-50 rounded-lg border border-red-200">
              <AlertTriangle className="h-5 w-5 text-red-600 mt-0.5" />
              <div className="flex-1">
                <h4 className="font-medium text-red-900">
                  Reset All Beta Data
                </h4>
                <p className="text-sm text-red-600 mt-1">
                  Completely reset all beta program data. This cannot be undone.
                </p>
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button
                      variant="destructive"
                      size="sm"
                      className="mt-3"
                      disabled={loading}
                    >
                      {loading ? "Resetting..." : "Reset All Data"}
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Reset All Beta Data?</AlertDialogTitle>
                      <AlertDialogDescription>
                        This will permanently delete all your beta program data
                        including:
                        <br />• Onboarding progress and preferences
                        <br />• Beta-specific settings
                        <br />• Feedback submissions
                        <br />• Finance type selection
                        <br />
                        <br />
                        <strong>This action cannot be undone.</strong> You will
                        need to rejoin the beta program.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction
                        onClick={handleResetAllBetaData}
                        disabled={loading}
                        className="bg-red-600 hover:bg-red-700"
                      >
                        {loading ? "Resetting..." : "Reset All Data"}
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Debug Information */}
      {process.env.NODE_ENV === "development" && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardHeader>
            <CardTitle className="text-yellow-800">Debug Information</CardTitle>
            <CardDescription className="text-yellow-600">
              Development mode debug information
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-xs text-yellow-700 space-y-2">
              <div>
                <strong>User ID:</strong> {user.id}
              </div>
              <div>
                <strong>Finance Type:</strong> {financeType}
              </div>
              <div>
                <strong>Onboarding Completed:</strong>{" "}
                {user.onboarding_completed ? "Yes" : "No"}
              </div>
              <div>
                <strong>Beta User:</strong> {user.beta_user ? "Yes" : "No"}
              </div>
              <div>
                <strong>LocalStorage Keys:</strong>
              </div>
              <ul className="ml-4 list-disc">
                {typeof window !== "undefined" &&
                  Object.keys(localStorage)
                    .filter((key) => key.includes("beta"))
                    .map((key) => <li key={key}>{key}</li>)}
              </ul>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
