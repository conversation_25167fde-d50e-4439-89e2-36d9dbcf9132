"use client";

import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/shared/components/ui/card";
import { <PERSON><PERSON> } from "@/shared/components/ui/button";
import { Badge } from "@/shared/components/ui/badge";
import { Progress } from "@/shared/components/ui/progress";
import {
  BarChart3,
  User,
  Users,
  TrendingUp,
  Target,
  PieChart,
  Filter,
  Download,
  Eye,
  EyeOff,
} from "lucide-react";
import { useBetaFinanceType } from "@/beta/contexts/BetaFinanceTypeContext";
import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  TabsList,
  TabsTrigger,
} from "@/shared/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/shared/components/ui/select";

export default function UnifiedAnalyticsPage() {
  const { user } = useBetaFinanceType();
  const [timeRange, setTimeRange] = useState("6months");
  const [showPersonal, setShowPersonal] = useState(true);
  const [showFamily, setShowFamily] = useState(true);
  const [showAmounts, setShowAmounts] = useState(true);

  // Mock data for analytics - in real implementation, this would come from the combinedFinanceService
  const [analyticsData] = useState({
    timeRanges: {
      "1month": { label: "Last Month", periods: 1 },
      "3months": { label: "Last 3 Months", periods: 3 },
      "6months": { label: "Last 6 Months", periods: 6 },
      "1year": { label: "Last Year", periods: 12 },
    },
    trends: {
      personal: {
        income: [3200, 3400, 3300, 3600, 3500, 3800],
        expenses: [2800, 2900, 2700, 3100, 2950, 3200],
        balance: [42000, 42600, 43200, 43700, 44250, 44850],
      },
      family: {
        income: [1800, 2000, 1950, 2100, 2200, 2300],
        expenses: [1600, 1750, 1650, 1850, 1900, 2000],
        balance: [28000, 28250, 28550, 28800, 29100, 29400],
      },
    },
    spendingCategories: [
      { name: "Housing", personal: 1200, family: 800, color: "#8884d8" },
      { name: "Food", personal: 600, family: 400, color: "#82ca9d" },
      { name: "Transportation", personal: 400, family: 300, color: "#ffc658" },
      { name: "Entertainment", personal: 300, family: 200, color: "#ff7300" },
      { name: "Utilities", personal: 250, family: 150, color: "#0088fe" },
      { name: "Healthcare", personal: 200, family: 100, color: "#8dd1e1" },
    ],
  });

  const formatCurrency = (amount: number) => {
    if (!showAmounts) return "••••••";
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: user?.currency || "USD",
    }).format(amount);
  };

  const getTotalForCategory = (category: Record<string, unknown>) => {
    let total = 0;
    if (showPersonal) total += (category.personal as number) || 0;
    if (showFamily) total += (category.family as number) || 0;
    return total;
  };

  const getVisibleCategories = () => {
    return analyticsData.spendingCategories
      .map((category) => ({
        ...category,
        total: getTotalForCategory(category),
      }))
      .filter((category) => category.total > 0)
      .sort((a, b) => b.total - a.total);
  };

  const getCurrentTrends = () => {
    const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun"];
    return months.map((month, index) => ({
      month,
      personalIncome: showPersonal
        ? analyticsData.trends.personal.income[index]
        : 0,
      personalExpenses: showPersonal
        ? analyticsData.trends.personal.expenses[index]
        : 0,
      familyIncome: showFamily ? analyticsData.trends.family.income[index] : 0,
      familyExpenses: showFamily
        ? analyticsData.trends.family.expenses[index]
        : 0,
    }));
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Unified Financial Analytics
          </h1>
          <p className="text-muted-foreground">
            Advanced analytics and insights across your complete financial
            picture
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Filter className="w-4 h-4 mr-2" />
            Filters
          </Button>
          <Button variant="outline" size="sm">
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowAmounts(!showAmounts)}
          >
            {showAmounts ? (
              <Eye className="w-4 h-4" />
            ) : (
              <EyeOff className="w-4 h-4" />
            )}
          </Button>
        </div>
      </div>

      {/* Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Analytics Controls
          </CardTitle>
          <CardDescription>
            Customize your analytics view and data sources
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap items-center gap-4">
            <div className="flex items-center gap-2">
              <label htmlFor="timeRange" className="text-sm font-medium">
                Time Range:
              </label>
              <Select value={timeRange} onValueChange={setTimeRange}>
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(analyticsData.timeRanges).map(
                    ([key, range]) => (
                      <SelectItem key={key} value={key}>
                        {range.label}
                      </SelectItem>
                    )
                  )}
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center gap-4">
              <label className="text-sm font-medium">Data Sources:</label>
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="personal"
                  checked={showPersonal}
                  onChange={(e) => setShowPersonal(e.target.checked)}
                  className="rounded"
                />
                <label
                  htmlFor="personal"
                  className="text-sm flex items-center gap-1"
                >
                  <User className="w-3 h-3 text-blue-500" />
                  Personal
                </label>
              </div>
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="family"
                  checked={showFamily}
                  onChange={(e) => setShowFamily(e.target.checked)}
                  className="rounded"
                />
                <label
                  htmlFor="family"
                  className="text-sm flex items-center gap-1"
                >
                  <Users className="w-3 h-3 text-green-500" />
                  Family
                </label>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Key Metrics Summary */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="border-l-4 border-blue-500">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">
              Total Combined Balance
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold">
              {formatCurrency(
                (showPersonal ? 44850 : 0) + (showFamily ? 29400 : 0)
              )}
            </p>
            <div className="flex items-center gap-2 mt-1">
              {showPersonal && (
                <Badge variant="outline" className="text-xs">
                  Personal: {formatCurrency(44850)}
                </Badge>
              )}
              {showFamily && (
                <Badge variant="outline" className="text-xs">
                  Family: {formatCurrency(29400)}
                </Badge>
              )}
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-green-500">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">
              Monthly Income
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold">
              {formatCurrency(
                (showPersonal ? 3800 : 0) + (showFamily ? 2300 : 0)
              )}
            </p>
            <div className="flex items-center gap-1 mt-1">
              <TrendingUp className="w-3 h-3 text-green-500" />
              <span className="text-xs text-green-600">
                +8.5% vs last month
              </span>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-red-500">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">
              Monthly Expenses
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold">
              {formatCurrency(
                (showPersonal ? 3200 : 0) + (showFamily ? 2000 : 0)
              )}
            </p>
            <div className="flex items-center gap-1 mt-1">
              <TrendingUp className="w-3 h-3 text-red-500" />
              <span className="text-xs text-red-600">+5.2% vs last month</span>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-purple-500">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Savings Rate</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold">
              {(
                (((showPersonal ? 600 : 0) + (showFamily ? 300 : 0)) /
                  ((showPersonal ? 3800 : 0) + (showFamily ? 2300 : 0))) *
                100
              ).toFixed(1)}
              %
            </p>
            <div className="flex items-center gap-1 mt-1">
              <TrendingUp className="w-3 h-3 text-purple-500" />
              <span className="text-xs text-purple-600">
                +2.1% vs last month
              </span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Analytics Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="trends">Trends</TabsTrigger>
          <TabsTrigger value="categories">Categories</TabsTrigger>
          <TabsTrigger value="goals">Goals</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Cash Flow Chart */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Cash Flow Trends
                </CardTitle>
                <CardDescription>
                  Income vs expenses over the last 6 months
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {getCurrentTrends().map((month) => (
                    <div key={month.month} className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>{month.month}</span>
                        <span className="font-medium">
                          Net:{" "}
                          {formatCurrency(
                            month.personalIncome +
                              month.familyIncome -
                              (month.personalExpenses + month.familyExpenses)
                          )}
                        </span>
                      </div>
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                          <span className="text-xs">
                            Income:{" "}
                            {formatCurrency(
                              month.personalIncome + month.familyIncome
                            )}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                          <span className="text-xs">
                            Expenses:{" "}
                            {formatCurrency(
                              month.personalExpenses + month.familyExpenses
                            )}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Account Balance Distribution */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <PieChart className="h-5 w-5" />
                  Balance Distribution
                </CardTitle>
                <CardDescription>
                  How your money is distributed across accounts
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {showPersonal && (
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm flex items-center gap-2">
                          <User className="w-4 h-4 text-blue-500" />
                          Personal Accounts
                        </span>
                        <span className="font-medium">
                          {formatCurrency(44850)}
                        </span>
                      </div>
                      <Progress
                        value={
                          (44850 /
                            ((showPersonal ? 44850 : 0) +
                              (showFamily ? 29400 : 0))) *
                          100
                        }
                        className="h-2"
                      />
                    </div>
                  )}
                  {showFamily && (
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm flex items-center gap-2">
                          <Users className="w-4 h-4 text-green-500" />
                          Family Accounts
                        </span>
                        <span className="font-medium">
                          {formatCurrency(29400)}
                        </span>
                      </div>
                      <Progress
                        value={
                          (29400 /
                            ((showPersonal ? 44850 : 0) +
                              (showFamily ? 29400 : 0))) *
                          100
                        }
                        className="h-2"
                      />
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="categories" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <PieChart className="h-5 w-5" />
                Spending by Category
              </CardTitle>
              <CardDescription>
                Breakdown of expenses across different categories
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {getVisibleCategories().map((category) => (
                  <div key={category.name} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">
                        {category.name}
                      </span>
                      <span className="font-semibold">
                        {formatCurrency(category.total)}
                      </span>
                    </div>
                    <div className="flex gap-1">
                      {showPersonal && (
                        <div className="flex-1 space-y-1">
                          <div className="flex items-center justify-between text-xs">
                            <span className="flex items-center gap-1">
                              <User className="w-3 h-3 text-blue-500" />
                              Personal
                            </span>
                            <span>{formatCurrency(category.personal)}</span>
                          </div>
                          <Progress
                            value={(category.personal / category.total) * 100}
                            className="h-1"
                          />
                        </div>
                      )}
                      {showFamily && (
                        <div className="flex-1 space-y-1">
                          <div className="flex items-center justify-between text-xs">
                            <span className="flex items-center gap-1">
                              <Users className="w-3 h-3 text-green-500" />
                              Family
                            </span>
                            <span>{formatCurrency(category.family)}</span>
                          </div>
                          <Progress
                            value={(category.family / category.total) * 100}
                            className="h-1"
                          />
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="trends" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Financial Trends Analysis
              </CardTitle>
              <CardDescription>
                Detailed analysis of your financial patterns over time
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                <BarChart3 className="w-12 h-12 mx-auto mb-2 opacity-50" />
                <p>Advanced trend analysis charts would be displayed here</p>
                <p className="text-sm">
                  Integration with charting library (recharts) coming soon
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="goals" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                Goal Progress Analytics
              </CardTitle>
              <CardDescription>
                Track and analyze progress on your financial goals
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                <Target className="w-12 h-12 mx-auto mb-2 opacity-50" />
                <p>Goal analytics and forecasting would be displayed here</p>
                <p className="text-sm">
                  Real goal data integration coming soon
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Insights */}
      <Card className="border-l-4 border-blue-500 bg-blue-50/50">
        <CardContent className="pt-6">
          <div className="flex items-start gap-3">
            <div className="p-2 rounded-full bg-blue-100">
              <BarChart3 className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <h3 className="font-semibold text-blue-900">
                Advanced Analytics & Insights
              </h3>
              <p className="text-sm text-blue-700 mt-1">
                This unified analytics view provides deep insights into your
                financial patterns, combining personal and family data for
                comprehensive analysis. Use the controls above to filter and
                customize your view for specific insights.
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                <div className="bg-white rounded-lg p-3 border">
                  <h4 className="font-medium text-blue-900 text-sm">
                    🔍 Pattern Recognition
                  </h4>
                  <p className="text-xs text-blue-700 mt-1">
                    Identify spending patterns and financial trends across time
                    periods
                  </p>
                </div>
                <div className="bg-white rounded-lg p-3 border">
                  <h4 className="font-medium text-blue-900 text-sm">
                    📊 Predictive Modeling
                  </h4>
                  <p className="text-xs text-blue-700 mt-1">
                    Forecast future financial outcomes based on current trends
                  </p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
