"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/shared/components/ui/card';
import { Button } from '@/shared/components/ui/button';
import { Badge } from '@/shared/components/ui/badge';
import { Progress } from '@/shared/components/ui/progress';
import { 
  TestTube, 
  Play, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  Database,
  User,
  Settings,
  MessageSquare
} from 'lucide-react';
import { BetaIntegrationTester } from '@/beta/test-integration';

interface TestResult {
  name: string;
  passed: boolean;
  message: string;
  details?: Record<string, unknown>;
}

export default function BetaTestPage() {
  const [isRunning, setIsRunning] = useState(false);
  const [results, setResults] = useState<TestResult[]>([]);
  const [progress, setProgress] = useState(0);

  const runTests = async () => {
    setIsRunning(true);
    setResults([]);
    setProgress(0);

    try {
      const tester = new BetaIntegrationTester();
      
      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setProgress(prev => Math.min(prev + 10, 90));
      }, 200);

      const testResults = await tester.runAllTests();
      
      clearInterval(progressInterval);
      setProgress(100);
      setResults(testResults);
    } catch (error) {
      console.error('Test execution error:', error);
      setResults([{
        name: 'Test Execution',
        passed: false,
        message: 'Failed to execute tests',
        details: error as Record<string, unknown>
      }]);
    } finally {
      setIsRunning(false);
    }
  };

  const getTestIcon = (result: TestResult) => {
    if (result.passed) {
      return <CheckCircle className="h-5 w-5 text-green-600" />;
    } else {
      return <XCircle className="h-5 w-5 text-red-600" />;
    }
  };

  const getTestCategoryIcon = (testName: string) => {
    if (testName.includes('Database')) return <Database className="h-4 w-4" />;
    if (testName.includes('User') || testName.includes('Auth')) return <User className="h-4 w-4" />;
    if (testName.includes('Service') || testName.includes('Factory')) return <Settings className="h-4 w-4" />;
    if (testName.includes('Feedback')) return <MessageSquare className="h-4 w-4" />;
    return <TestTube className="h-4 w-4" />;
  };

  const passedTests = results.filter(r => r.passed).length;
  const totalTests = results.length;
  const allPassed = totalTests > 0 && passedTests === totalTests;

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <TestTube className="h-6 w-6" />
          <h1 className="text-2xl font-bold">Beta Integration Tests</h1>
          <Badge variant="secondary" className="bg-gradient-to-r from-blue-600 to-purple-600 text-white">
            BETA
          </Badge>
        </div>
        
        <Button 
          onClick={runTests} 
          disabled={isRunning}
          className="flex items-center space-x-2"
        >
          <Play className="h-4 w-4" />
          <span>{isRunning ? 'Running Tests...' : 'Run Tests'}</span>
        </Button>
      </div>

      {/* Test Overview */}
      <Card>
        <CardHeader>
          <CardTitle>Integration Test Suite</CardTitle>
          <CardDescription>
            Verify that the beta program backend integration is working correctly.
            This will test database connectivity, schema readiness, and service functionality.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Progress */}
          {isRunning && (
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Running tests...</span>
                <span>{progress}%</span>
              </div>
              <Progress value={progress} className="w-full" />
            </div>
          )}

          {/* Results Summary */}
          {results.length > 0 && (
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                {allPassed ? (
                  <CheckCircle className="h-5 w-5 text-green-600" />
                ) : (
                  <AlertTriangle className="h-5 w-5 text-amber-600" />
                )}
                <span className="font-medium">
                  {passedTests}/{totalTests} tests passed
                </span>
              </div>
              
              <Badge 
                variant={allPassed ? "default" : "destructive"}
                className={allPassed ? "bg-green-600" : ""}
              >
                {allPassed ? 'All Tests Passed' : 'Some Tests Failed'}
              </Badge>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Test Results */}
      {results.length > 0 && (
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Test Results</h2>
          
          {results.map((result, index) => (
            <Card key={index} className={`border-l-4 ${
              result.passed ? 'border-l-green-500' : 'border-l-red-500'
            }`}>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    {getTestCategoryIcon(result.name)}
                    <CardTitle className="text-lg">{result.name}</CardTitle>
                  </div>
                  {getTestIcon(result)}
                </div>
                <CardDescription>{result.message}</CardDescription>
              </CardHeader>
              
              {result.details && (
                <CardContent className="pt-0">
                  <details className="space-y-2">
                    <summary className="cursor-pointer text-sm font-medium text-muted-foreground hover:text-foreground">
                      View Details
                    </summary>
                    <pre className="text-xs bg-muted p-3 rounded-md overflow-auto">
                      {JSON.stringify(result.details, null, 2)}
                    </pre>
                  </details>
                </CardContent>
              )}
            </Card>
          ))}
        </div>
      )}

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>Before Running Tests</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="space-y-2">
            <h4 className="font-medium">1. Database Setup</h4>
            <p className="text-sm text-muted-foreground">
              Make sure your Supabase project is active and the beta schema migration has been applied.
              See <code>src/beta/migrations/README.md</code> for instructions.
            </p>
          </div>
          
          <div className="space-y-2">
            <h4 className="font-medium">2. Environment Variables</h4>
            <p className="text-sm text-muted-foreground">
              Ensure your <code>.env.local</code> file has the correct Supabase credentials.
            </p>
          </div>
          
          <div className="space-y-2">
            <h4 className="font-medium">3. Authentication</h4>
            <p className="text-sm text-muted-foreground">
              Sign in to test user-specific features like profile updates and feedback submission.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* What Tests Check */}
      <Card>
        <CardHeader>
          <CardTitle>What These Tests Check</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Database className="h-4 w-4 text-blue-600" />
                <span className="font-medium">Database Connection</span>
              </div>
              <p className="text-sm text-muted-foreground">
                Verifies connection to Supabase database
              </p>
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Settings className="h-4 w-4 text-green-600" />
                <span className="font-medium">Beta Schema</span>
              </div>
              <p className="text-sm text-muted-foreground">
                Checks if beta tables and fields exist
              </p>
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <User className="h-4 w-4 text-purple-600" />
                <span className="font-medium">User Authentication</span>
              </div>
              <p className="text-sm text-muted-foreground">
                Tests user authentication and profile access
              </p>
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <MessageSquare className="h-4 w-4 text-amber-600" />
                <span className="font-medium">Beta Services</span>
              </div>
              <p className="text-sm text-muted-foreground">
                Tests beta user operations and feedback
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
