import type { NextConfig } from "next";
import lingoCompiler from "lingo.dev/compiler";

const nextConfig: NextConfig = {
  /* config options here */
  experimental: {
    // Enable experimental features if needed
  },
};

export default lingoCompiler.next({
  sourceLocale: "en",
  targetLocales: ["nl", "de", "fr"], // Dutch, German, French (English is source)
  models: {
    "*:*": "google:gemini-1.5-flash", // Using Google Gemini for translations (supports all locales)
  },
  sourceRoot: "src", // Our source code is in src directory
  lingoDir: "lingo", // Translation files directory
  rsc: true, // Enable React Server Components support
  debug: false, // Disable debug logging for cleaner output
})(nextConfig);
